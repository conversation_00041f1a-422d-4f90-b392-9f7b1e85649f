<!DOCTYPE html>
<html lang="es" class="scroll-smooth">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Filmación con Drones FPV - Dronexperto</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;600;700&display=swap" rel="stylesheet">
    <style>
        body {
            font-family: 'Inter', sans-serif;
            background-color: #000000;
            color: #ffffff;
            overflow-x: hidden;
        }
        #bg-canvas {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            z-index: -1;
        }
        .slide {
            height: 100vh;
            width: 100vw;
            display: flex;
            justify-content: center;
            align-items: center;
            scroll-snap-align: start;
            padding: 2rem;
            box-sizing: border-box;
        }
        main {
            scroll-snap-type: y mandatory;
            overflow-y: scroll;
            height: 100vh;
        }
        .content-card {
            background-color: rgba(0, 0, 0, 0.8);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.1);
            border-radius: 1.5rem;
            padding: 2rem;
            max-width: 800px;
            width: 100%;
            box-shadow: 0 8px 32px 0 rgba(0, 0, 0, 0.37);
        }
        .text-dronexperto {
  color: #dbfe0d; /* tu color personalizado */
}
.bg-dronexperto {
  background-color: #dbfe0d; /* reemplazo de bg-dronexperto */
}
        /* Custom scrollbar for a cleaner look */
        main::-webkit-scrollbar {
            width: 5px;
        }
        main::-webkit-scrollbar-track {
            background: #111827;
        }
        main::-webkit-scrollbar-thumb {
            background: #dbfe0d; /* lime-400 */
            border-radius: 10px;
        }
        /* Lightbox styles */
        .lightbox {
            transition: opacity 0.3s ease;
        }
        .lightbox-content {
            transition: transform 0.3s ease;
        }
    </style>
</head>
<body>
    <canvas id="bg-canvas"></canvas>

    <main>
        <!-- Slide 1: Quiénes Somos -->
        <section id="slide-1" class="slide">
            <div class="content-card text-center">
                <div class="flex justify-center items-center mb-6">
  <img src="images/dronexperto_logo_neon.svg" alt="Logo Dronexperto Neon" class="w-48 h-auto">
</div>
                <h2 class="text-2xl font-semibold mb-6">Pioneros del FPV en México</h2>
                <p class="text-lg text-gray-300">
                    Fundada en 2014, Dronexperto nació como la primera distribuidora en México enfocada en el sector de drones FPV. Hoy, hemos evolucionado y expandido nuestros servicios a cuatro áreas clave: <strong>filmación profesional, diseño de drones personalizados, educación y shows de drones</strong>. Aunque la distribución sigue siendo parte de nosotros, nuestra pasión es crear experiencias visuales únicas y formar a la nueva generación de pilotos.
                </p>
            </div>
        </section>

        <!-- Slide 2: Showreel Video -->
        <section id="slide-2" class="slide">
            <div class="content-card w-full max-w-4xl">
                <h1 class="text-3xl md:text-4xl font-bold text-dronexperto mb-6 text-center">Nuestro Showreel</h1>
                <div style="padding:56.25% 0 0 0;position:relative;">
                    <iframe src="https://player.vimeo.com/video/790607149?autoplay=0&loop=1&muted=1" style="position:absolute;top:0;left:0;width:100%;height:100%; border-radius: 1rem;" frameborder="0" allow="autoplay; fullscreen; picture-in-picture" allowfullscreen></iframe>
                </div>
            </div>
        </section>

        <!-- Slide 3: Nuestra Trayectoria -->
        <section id="slide-3" class="slide">
            <div class="content-card">
                 <h1 class="text-3xl md:text-4xl font-bold text-dronexperto mb-6 text-center">Nuestra Trayectoria</h1>
                 <p class="text-lg text-gray-300 text-center mb-8">
                    Con casi 11 años en la comunidad, hemos dado vida a proyectos de todo tipo. La precisión, la energía y la captura de la adrenalina son nuestro sello.
                </p>
                <div class="grid grid-cols-1 md:grid-cols-3 gap-6 text-center">
                    <div class="bg-gray-800/50 p-4 rounded-xl">
                        <h3 class="text-xl font-bold text-lime-300 mb-2">Festivales y Conciertos</h3>
                        <p class="text-gray-400">Capturamos la energía de eventos masivos desde perspectivas únicas e inmersivas.</p>
                    </div>
                    <div class="bg-gray-800/50 p-4 rounded-xl">
                        <h3 class="text-xl font-bold text-lime-300 mb-2">Corporativo y Cine</h3>
                        <p class="text-gray-400">Aportamos valor cinematográfico a comerciales, producciones de TV y contenido de marca.</p>
                    </div>
                    <div class="bg-gray-800/50 p-4 rounded-xl">
                        <h3 class="text-xl font-bold text-lime-300 mb-2">Deportes de Motor</h3>
                        <p class="text-gray-400">Nuestra pasión personal: seguir la acción a alta velocidad para tomas llenas de adrenalina.</p>
                    </div>
                </div>
            </div>
        </section>

        <!-- Slide 4: History - Intro -->
        <section id="slide-4" class="slide">
            <div class="content-card text-center">
                <h1 class="text-3xl md:text-4xl font-bold text-dronexperto mb-6">La Historia del FPV</h1>
                <p class="text-lg text-gray-300">
                    La trayectoria de la tecnología FPV es una fascinante crónica de innovación, impulsada no por grandes corporaciones, sino por la pasión de una comunidad global de aficionados. Aunque sus raíces se encuentran en los UAVs de uso militar, la historia del vuelo en primera persona es un testamento al ingenio colectivo.
                </p>
            </div>
        </section>

        <!-- Slide 5: History - 1907 -->
        <section id="slide-5" class="slide">
            <div class="content-card text-center">
                <h1 class="text-3xl md:text-4xl font-bold text-dronexperto mb-6">Los Orígenes del Cuadricóptero</h1>
                <p class="text-lg text-gray-300 mb-6">
                    Por increíble que parezca, el concepto de cuadricóptero data de mucho antes de la era digital. El registro más antiguo que se tiene es de 1907 con el <strong>Gyroplane de Breguet-Richet</strong>, el primer aparato en elevarse del suelo con un piloto y cuatro rotores.
                </p>
                <button onclick="openImageLightbox('images/1907.jpg')" class="bg-dronexperto hover:bg-dronexperto text-black font-bold py-2 px-6 rounded-full transition duration-300 transform hover:scale-105">
                    Ver Gyroplane (1907)
                </button>
            </div>
        </section>
        
        <!-- Slide 6: History - 1922 -->
        <section id="slide-6" class="slide">
             <div class="content-card text-center">
                <h1 class="text-3xl md:text-4xl font-bold text-dronexperto mb-6">Avances y la Era R/C</h1>
                <p class="text-lg text-gray-300 mb-6">
                   En 1922, el <strong>Helicóptero de Bothezat</strong>, encargado por el ejército de EE.UU., logró su primer vuelo exitoso, demostrando un mayor control. Tras la Segunda Guerra Mundial, la tecnología de radiocontrol (R/C) fue adoptada por entusiastas, sentando las bases para el futuro del vuelo no tripulado.
                </p>
                <button onclick="openYoutubeLightbox('oM6TqjHfC5I')" class="bg-dronexperto hover:bg-dronexperto text-black font-bold py-2 px-6 rounded-full transition duration-300 transform hover:scale-105">
                    Ver Vuelo de Bothezat (1922)
                </button>
            </div>
        </section>
        
        <!-- Slide 7: History - Pioneers -->
        <section id="slide-7" class="slide">
             <div class="content-card text-center">
                <h1 class="text-3xl md:text-4xl font-bold text-dronexperto mb-6">Los Pioneros del FPV</h1>
                <p class="text-lg text-gray-300 mb-6">
                    En la década de 1990, a pesar de las limitaciones de equipos voluminosos y costosos, <strong>Carl Berry con su "Project Cyclops"</strong> fue un pionero notable del FPV. Logró montar pesadas cámaras de seguridad en enormes aeromodelos de 3 metros de envergadura para transmitir una señal de vídeo rudimentaria, demostrando una visión y una pasión inquebrantables por el vuelo en primera persona.
                </p>
                <button onclick="openYoutubeLightbox('fL8rIUkXAco')" class="bg-dronexperto hover:bg-dronexperto text-black font-bold py-2 px-6 rounded-full transition duration-300 transform hover:scale-105">
                    Ver un ejemplo de Project Cyclops
                </button>
            </div>
        </section>
        
        <!-- Slide 8: History - Modern Era -->
        <section id="slide-8" class="slide">
             <div class="content-card">
                <h1 class="text-3xl md:text-4xl font-bold text-dronexperto mb-6 text-center">La Revolución Digital y Comunitaria</h1>
                <p class="text-lg text-gray-300">
                   El verdadero punto de inflexión llegó con los foros en línea de los 2000 y, crucialmente, con la producción en masa de componentes para la <strong>Nintendo Wii</strong>. El acelerómetro y el giroscopio se volvieron económicos y accesibles, catalizando el desarrollo de controladores de vuelo modernos. Las carreras de drones, que comenzaron alrededor de 2011, actuaron como un campo de pruebas extremo, empujando los límites y dando lugar a los drones ligeros y potentes que hoy son indispensables en la industria cinematográfica.
                </p>
            </div>
        </section>

        <!-- Slide 9: Precursores del FPV en Nuestra Generación -->
        <section id="slide-9" class="slide">
            <div class="content-card text-center">
                <h1 class="text-3xl md:text-4xl font-bold text-dronexperto mb-6">Precursores del FPV en Nuestra Generación</h1>
                <p class="text-lg text-gray-300 mb-8">
                    Estos videos muestran el nivel de creatividad y habilidad que ha alcanzado la comunidad FPV, inspirando a la nueva generación de pilotos y cineastas:
                </p>
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <button onclick="openYoutubeLightbox('NsxyV-kgfio')" class="bg-dronexperto hover:bg-dronexperto text-black font-bold py-2 px-6 rounded-full transition duration-300 transform hover:scale-105">
                        Crash Season
                    </button>
                    <button onclick="openYoutubeLightbox('YhJpdCZNSDc')" class="bg-dronexperto hover:bg-dronexperto text-black font-bold py-2 px-6 rounded-full transition duration-300 transform hover:scale-105">
                        Dive Chernobyl
                    </button>
                    <button onclick="openYoutubeLightbox('1MBW8zoZUR4')" class="bg-dronexperto hover:bg-dronexperto text-black font-bold py-2 px-6 rounded-full transition duration-300 transform hover:scale-105">
                        Charpu
                    </button>
                    <button onclick="openYoutubeLightbox('eOdk7s87AGk')" class="bg-dronexperto hover:bg-dronexperto text-black font-bold py-2 px-6 rounded-full transition duration-300 transform hover:scale-105">
                        FPV Crashes
                    </button>
                </div>
            </div>
        </section>

        <!-- Slide 10: ¿Qué es un Drone FPV? -->
        <section id="slide-10" class="slide">
            <div class="content-card">
                <h1 class="text-3xl md:text-4xl font-bold text-dronexperto mb-6 text-center">¿Qué es un Drone FPV?</h1>
                <p class="text-lg text-gray-300 mb-4">
                    FPV son las siglas de "First Person View" o "Vista en Primera Persona". Un drone FPV transmite video en tiempo real desde una cámara a bordo directamente a unas gafas o monitor que usa el piloto.
                </p>
                <p class="text-lg text-gray-300">
                    Esta inmersión total permite un control preciso y dinámico, haciendo sentir al piloto como si estuviera volando dentro de la aeronave. A diferencia de los drones convencionales, los FPV se pilotan de forma completamente manual, ofreciendo una libertad de movimiento tridimensional sin igual.
                </p>
            </div>
        </section>

        <!-- Slide 11: Componentes Clave -->
        <section id="slide-11" class="slide">
            <div class="content-card">
                 <h1 class="text-3xl md:text-4xl font-bold text-dronexperto mb-6 text-center">Componentes de un Drone FPV</h1>
                <div class="grid grid-cols-2 md:grid-cols-3 gap-4 text-center">
                    <div class="bg-gray-800/50 p-3 rounded-xl flex flex-col items-center justify-between">
                        <h3 class="text-lg font-bold text-lime-300 mb-2">Frame (Chasis)</h3>
                        <button onclick="openImageLightbox('https://dronexperto.com/wp-content/uploads/2018/06/tbs_source_one_5.jpg')" class="mt-auto bg-lime-700 hover:bg-dronexperto text-white text-sm font-bold py-1 px-3 rounded-full transition duration-300">Ver Imagen</button>
                    </div>
                    <div class="bg-gray-800/50 p-3 rounded-xl flex flex-col items-center justify-between">
                        <h3 class="text-lg font-bold text-lime-300 mb-2">Motores</h3>
                        <button onclick="openImageLightbox('https://dronexperto.com/wp-content/uploads/2020/12/Ethix-Konasty-4.jpg')" class="mt-auto bg-lime-700 hover:bg-dronexperto text-white text-sm font-bold py-1 px-3 rounded-full transition duration-300">Ver Imagen</button>
                    </div>
                    <div class="bg-gray-800/50 p-3 rounded-xl flex flex-col items-center justify-between">
                        <h3 class="text-lg font-bold text-lime-300 mb-2">ESC (Variadores)</h3>
                        <button onclick="openImageLightbox('https://www.racedayquads.com/cdn/shop/files/runcam-speedybee-bls-8bit-55a-3-6s-30x30-4in1-esc-esc-30840779145329.jpg?v=1737103919&width=1200')" class="mt-auto bg-lime-700 hover:bg-dronexperto text-white text-sm font-bold py-1 px-3 rounded-full transition duration-300">Ver Imagen</button>
                    </div>
                    <div class="bg-gray-800/50 p-3 rounded-xl flex flex-col items-center justify-between">
                        <h3 class="text-lg font-bold text-lime-300 mb-2">Controlador de Vuelo</h3>
                        <button onclick="openImageLightbox('https://cdn11.bigcommerce.com/s-fhxxhuiq8q/images/stencil/1280x1280/products/133/520/09__70548.1618994905.jpg?c=2')" class="mt-auto bg-lime-700 hover:bg-dronexperto text-white text-sm font-bold py-1 px-3 rounded-full transition duration-300">Ver Imagen</button>
                    </div>
                    <div class="bg-gray-800/50 p-3 rounded-xl flex flex-col items-center justify-between">
                        <h3 class="text-lg font-bold text-lime-300 mb-2">Cámara FPV</h3>
                        <button onclick="openImageLightbox('https://rotorriot.com/cdn/shop/files/o3cam1_800x.png?v=1688755624')" class="mt-auto bg-lime-700 hover:bg-dronexperto text-white text-sm font-bold py-1 px-3 rounded-full transition duration-300">Ver Imagen</button>
                    </div>
                    <div class="bg-gray-800/50 p-3 rounded-xl flex flex-col items-center justify-between">
                        <h3 class="text-lg font-bold text-lime-300 mb-2">VTX y Antena</h3>
                        <button onclick="openImageLightbox('https://www.cined.com/content/uploads/2022/11/DJI-O3-Air-Unit-setup.jpg')" class="mt-auto bg-lime-700 hover:bg-dronexperto text-white text-sm font-bold py-1 px-3 rounded-full transition duration-300">Ver Imagen</button>
                    </div>
                    <div class="bg-gray-800/50 p-3 rounded-xl flex flex-col items-center justify-between">
                        <h3 class="text-lg font-bold text-lime-300 mb-2">Hélices</h3>
                        <button onclick="openImageLightbox('https://dronexperto.com/wp-content/uploads/2021/03/PROPELA.jpg')" class="mt-auto bg-lime-700 hover:bg-dronexperto text-white text-sm font-bold py-1 px-3 rounded-full transition duration-300">Ver Imagen</button>
                    </div>
                    <div class="bg-gray-800/50 p-3 rounded-xl flex flex-col items-center justify-between">
                        <h3 class="text-lg font-bold text-lime-300 mb-2">Batería LiPo</h3>
                        <button onclick="openImageLightbox('https://www.flydonkeydrone.com/cdn/shop/products/1200mah-6s-150c-04_1500x.jpg?v=1676497384')" class="mt-auto bg-lime-700 hover:bg-dronexperto text-white text-sm font-bold py-1 px-3 rounded-full transition duration-300">Ver Imagen</button>
                    </div>
                    <div class="bg-gray-800/50 p-3 rounded-xl flex flex-col items-center justify-between">
                        <h3 class="text-lg font-bold text-lime-300 mb-2">Receptor de Radio</h3>
                        <button onclick="openImageLightbox('https://www.team-blacksheep.com/img/gallery/A7302323.JPG')" class="mt-auto bg-lime-700 hover:bg-dronexperto text-white text-sm font-bold py-1 px-3 rounded-full transition duration-300">Ver Imagen</button>
                    </div>
                </div>
            </div>
        </section>

        <!-- Slide 12: Tipos de Drones FPV -->
        <section id="slide-12" class="slide">
            <div class="content-card">
                <h1 class="text-3xl md:text-4xl font-bold text-dronexperto mb-6 text-center">Tipos de Drones FPV</h1>
                <ul class="list-disc list-inside text-gray-300 space-y-2 text-lg">
                    <li><strong onclick="openImageLightbox('https://dronexperto.com/wp-content/uploads/2018/04/remix_recortado.png')">Freestyle:</strong> Para acrobacias y vuelo creativo en espacios abiertos.</li>
                    <li><strong onclick="openImageLightbox('https://dronexperto.com/wp-content/uploads/2017/07/drone_movimiento_morado.png')">Carreras:</strong onclick="openImageLightbox('')"> Ligeros y rápidos, optimizados para competir en circuitos.</li>
                    <li><strong onclick="openImageLightbox('https://gaotek.com/wp-content/uploads/2024/08/GAOTek-LWD-108-1.png')">Largo Alcance:</strong> Equipados para volar grandes distancias y explorar.</li>
                    <li><strong onclick="openImageLightbox('https://dronexperto.com/wp-content/uploads/2022/04/drone-verde-4.jpg')">Cinewhoop:</strong> Pequeños y seguros, ideales para tomas cinematográficas en interiores.</li>
                    <li><strong onclick="openImageLightbox('https://dronemania.es/wp-content/uploads/2019/05/98D81D11-28F0-4060-962C-8BC402B8A0EC.jpeg')">Toothpick:</strong> Ultraligeros y ágiles, llevando el minimalismo al extremo.</li>
                    <li><strong onclick="openImageLightbox('https://rotorbuilds.com/image.php?w=600&h=600&s=true&url=/pictures/f_1204_PYZe4SfqhM5KtCo5lSZ6G6ZBA.jpg')">X-Class:</strong> Drones gigantes para carreras de alta velocidad y espectáculo.</li>
                    <li><strong onclick="openImageLightbox('https://encrypted-tbn0.gstatic.com/images?q=tbn:ANd9GcQ3p5AH40Tt-44YvYohFz9qveIEfkvSOelgpQ&s')">Heavy Lifters:</strong> Diseñados para cargar cámaras de cine profesionales.</li>
                </ul>
            </div> 
        </section>

        <!-- Slide 13: Consejos para Aprender a Volar -->
        <section id="slide-13" class="slide">
            <div class="content-card">
                <h1 class="text-3xl md:text-4xl font-bold text-dronexperto mb-6 text-center">Consejos para Volar Mejor y Más Rápido</h1>
                 <ul class="list-disc list-inside text-gray-300 space-y-3 text-lg">
                    <li><strong>Usa un Simulador:</strong> Es la forma más rápida y segura de desarrollar memoria muscular sin romper tu equipo.</li>
                    <li><strong>Domina la Altitud:</strong> El control constante de la altitud es el secreto para atravesar espacios reducidos y lograr vuelos fluidos. Practica líneas rectas a ras de suelo.</li>
                    <li><strong>Estudia el Encuadre:</strong> Un gran piloto no solo vuela bien, sino que compone bien. Aprende sobre cinematografía y fotografía para que tus tomas cuenten una historia.</li>
                    <li><strong>Menos Piruetas, Más Precisión:</strong> En el mundo profesional, las líneas limpias y los encuadres perfectos son más valiosos que las acrobacias. La sutileza diferencia a un profesional.</li>
                </ul>
            </div>
        </section>

        <!-- Slide 14: Tomas Clave en Filmación FPV -->
        <section id="slide-14" class="slide">
            <div class="content-card">
                 <h1 class="text-3xl md:text-4xl font-bold text-dronexperto mb-6 text-center">Tomas Clave en Filmación FPV</h1>
                <p class="text-lg text-gray-300 mb-4">Los drones FPV permiten crear tomas que antes eran imposibles. Estas son algunas de las más impactantes:</p>
                <ul class="grid grid-cols-1 md:grid-cols-2 gap-x-8 gap-y-4 text-gray-300 text-lg">
                    <li><strong>Chase:</strong> Seguir a un sujeto en movimiento (auto, persona, etc.).</li>
                    <li><strong>Reveal:</strong> Revelar un paisaje o sujeto al superar un obstáculo.</li>
                    <li><strong>Dive:</strong> "Caer" desde una gran altura, siguiendo la línea de un edificio o montaña.</li>
                    <li><strong>Fly-Through:</strong> Atravesar ventanas, arcos o espacios estrechos.</li>
                    <li><strong>Orbit:</strong> Girar 360° alrededor de un punto de interés.</li>
                    <li><strong>Proximity Flying:</strong> Volar muy cerca de objetos o el suelo para crear una sensación de velocidad extrema.</li>
                </ul>
            </div>
        </section>

        <!-- Slide 15: La Revolución del Cinewhoop -->
        <section id="slide-15" class="slide">
            <div class="content-card">
                <h1 class="text-3xl md:text-4xl font-bold text-dronexperto mb-6 text-center">La Revolución del Cinewhoop</h1>
                <p class="text-lg text-gray-300">
                    Los Cinewhoops cambiaron la industria. Gracias a su tamaño reducido y hélices protegidas, ofrecen una flexibilidad 360° que ninguna grúa o dolly podría igualar. Permiten obtener ángulos íntimos y dinámicos, volando a través de espacios y cerca de personas con una seguridad sin precedentes. Pioneros como Robert McIntosh (creador de ReelSteady) nos mostraron el potencial de montar cámaras de alta calidad en estos pequeños drones, abriendo un mundo de posibilidades creativas que continúan redefiniendo la cinematografía.
                </p>
                <div class="text-center mt-6">
                    <button onclick="openYoutubeLightbox('w2j6m8tssJA')" class="bg-dronexperto hover:bg-dronexperto text-black font-bold py-2 px-6 rounded-full transition duration-300 transform hover:scale-105">
                        Ver Video
                    </button>
                </div>
            </div>
        </section>

        <!-- Slide 16: Equipo Esencial -->
        <section id="slide-16" class="slide">
            <div class="content-card">
                <h1 class="text-3xl md:text-4xl font-bold text-dronexperto mb-6 text-center">Equipo Esencial para Empezar</h1>
                <p class="text-lg text-gray-300 mb-4">Para iniciarte en la filmación FPV, necesitarás un kit básico:</p>
                <ul class="list-disc list-inside text-gray-300 space-y-2 text-lg">
                    <li><strong>Un drone FPV:</strong> Recomendamos un Cinewhoop o un kit "Ready-To-Fly" (RTF) para empezar.</li>
                    <li><strong>Gafas FPV:</strong> Tu ventana al mundo aéreo. La calidad aquí es importante.</li>
                    <li><strong>Radiocontrol:</strong> Tu conexión con el drone. La precisión y fiabilidad son clave.</li>
                    <li><strong>Baterías y Cargador:</strong> Necesitarás varias baterías LiPo y un cargador inteligente y seguro.</li>
                    <li><strong>Herramientas Básicas:</strong> Un set de destornilladores y soldador para reparaciones.</li>
                </ul>
            </div>
        </section>

        <!-- Slide 17: Seguridad y Regulaciones (General) -->
        <section id="slide-17" class="slide">
            <div class="content-card">
                 <h1 class="text-3xl md:text-4xl font-bold text-dronexperto mb-6 text-center">Seguridad y Profesionalismo</h1>
                <p class="text-lg text-gray-300">
                    Volar seguro es tu máxima prioridad. Un piloto profesional es, ante todo, un piloto responsable. Esto implica conocer tu equipo, planificar cada vuelo y operar siempre dentro de un marco legal. En las siguientes secciones, detallaremos la normativa específica para México.
                </p>
            </div>
        </section>

        <!-- Slide 18: NOM-107 Introduction -->
        <section id="slide-18" class="slide">
            <div class="content-card">
                 <h1 class="text-3xl md:text-4xl font-bold text-dronexperto mb-6 text-center">Regulación en México: NOM-107</h1>
                <p class="text-lg text-gray-300">
                    En México, la operación de drones (RPAS) está regulada por la <strong>Agencia Federal de Aviación Civil (AFAC)</strong> a través de la norma <strong>NOM-107-SCT3-2019</strong>. Su objetivo es garantizar la seguridad del espacio aéreo, las personas y sus bienes en tierra. La norma clasifica los drones por su peso y tipo de uso (recreativo o comercial), estableciendo requisitos específicos para cada categoría.
                </p>
            </div>
        </section>

        <!-- Slide 19: Pilot License -->
        <section id="slide-19" class="slide">
            <div class="content-card">
                 <h1 class="text-3xl md:text-4xl font-bold text-dronexperto mb-6 text-center">Requisito 1: Licencia de Piloto</h1>
                <p class="text-lg text-gray-300">
                    Para operar un drone de más de 250 gramos con fines comerciales, es <strong>obligatorio contar con una Licencia de Piloto de RPAS</strong> emitida por la AFAC. Obtenerla requiere:
                </p>
                <ul class="list-disc list-inside text-gray-300 space-y-2 text-lg mt-4">
                    <li>Aprobar un curso teórico y práctico en un centro de capacitación autorizado.</li>
                    <li>Pasar un examen médico aeronáutico.</li>
                    <li>Demostrar conocimientos en reglamentación aérea, meteorología y performance de la aeronave.</li>
                </ul>
            </div>
        </section>

        <!-- Slide 20: Drone Registration -->
        <section id="slide-20" class="slide">
            <div class="content-card">
                 <h1 class="text-3xl md:text-4xl font-bold text-dronexperto mb-6 text-center">Requisito 2: Registro del Drone</h1>
                <p class="text-lg text-gray-300">
                    Toda aeronave no tripulada (drone) con un peso superior a 250 gramos debe ser <strong>registrada ante la AFAC</strong>. Este proceso es gratuito y se realiza en línea. Al finalizar, se obtiene un folio de constancia de registro que funciona como la "matrícula" del drone. Este folio debe estar visible en la aeronave en todo momento durante su operación.
                </p>
            </div>
        </section>

        <!-- Slide 21: Essential Documentation -->
        <section id="slide-21" class="slide">
            <div class="content-card">
                 <h1 class="text-3xl md:text-4xl font-bold text-dronexperto mb-6 text-center">Requisito 3: Documentación Esencial</h1>
                <p class="text-lg text-gray-300">
                    Para operaciones profesionales, la AFAC exige contar con documentación clave que garantice la estandarización y seguridad de tus vuelos:
                </p>
                 <ul class="list-disc list-inside text-gray-300 space-y-2 text-lg mt-4">
                    <li><strong>Póliza de Seguro de Responsabilidad Civil:</strong> Obligatoria para cubrir posibles daños a terceros.</li>
                    <li><strong>Manual de Operaciones:</strong> Documento que detalla tus procedimientos de vuelo, protocolos de seguridad, mantenimiento y emergencias.</li>
                </ul>
            </div>
        </section>

        <!-- Slide 22: Key Operational Points -->
        <section id="slide-22" class="slide">
            <div class="content-card">
                 <h1 class="text-3xl md:text-4xl font-bold text-dronexperto mb-6 text-center">Puntos Clave de la Operación</h1>
                <p class="text-lg text-gray-300">
                    Al volar profesionalmente bajo la NOM-107, siempre debes:
                </p>
                 <ul class="list-disc list-inside text-gray-300 space-y-2 text-lg mt-4">
                    <li>Mantener el drone dentro de tu línea de visión (VLOS).</li>
                    <li>Respetar la altitud máxima de 122 metros sobre el terreno.</li>
                    <li>No volar sobre personas no involucradas en la operación.</li>
                    <li>Obtener permiso para volar sobre propiedad privada.</li>
                    <li>Mantener una distancia segura de aeropuertos y helipuertos.</li>
                </ul>
            </div>
        </section>

    </main>

    <footer class="fixed bottom-0 left-0 w-full flex justify-center p-4 z-10">
        <div class="flex flex-wrap justify-center gap-1 bg-gray-900/50 backdrop-blur-sm p-2 rounded-full border border-gray-700">
            <a href="#slide-1" class="w-6 h-6 flex items-center justify-center rounded-full hover:bg-dronexperto transition-colors text-xs">1</a>
            <a href="#slide-2" class="w-6 h-6 flex items-center justify-center rounded-full hover:bg-dronexperto transition-colors text-xs">2</a>
            <a href="#slide-3" class="w-6 h-6 flex items-center justify-center rounded-full hover:bg-dronexperto transition-colors text-xs">3</a>
            <a href="#slide-4" class="w-6 h-6 flex items-center justify-center rounded-full hover:bg-dronexperto transition-colors text-xs">4</a>
            <a href="#slide-5" class="w-6 h-6 flex items-center justify-center rounded-full hover:bg-dronexperto transition-colors text-xs">5</a>
            <a href="#slide-6" class="w-6 h-6 flex items-center justify-center rounded-full hover:bg-dronexperto transition-colors text-xs">6</a>
            <a href="#slide-7" class="w-6 h-6 flex items-center justify-center rounded-full hover:bg-dronexperto transition-colors text-xs">7</a>
            <a href="#slide-8" class="w-6 h-6 flex items-center justify-center rounded-full hover:bg-dronexperto transition-colors text-xs">8</a>
            <a href="#slide-9" class="w-6 h-6 flex items-center justify-center rounded-full hover:bg-dronexperto transition-colors text-xs">9</a>
            <a href="#slide-10" class="w-6 h-6 flex items-center justify-center rounded-full hover:bg-dronexperto transition-colors text-xs">10</a>
            <a href="#slide-11" class="w-6 h-6 flex items-center justify-center rounded-full hover:bg-dronexperto transition-colors text-xs">11</a>
            <a href="#slide-12" class="w-6 h-6 flex items-center justify-center rounded-full hover:bg-dronexperto transition-colors text-xs">12</a>
            <a href="#slide-13" class="w-6 h-6 flex items-center justify-center rounded-full hover:bg-dronexperto transition-colors text-xs">13</a>
            <a href="#slide-14" class="w-6 h-6 flex items-center justify-center rounded-full hover:bg-dronexperto transition-colors text-xs">14</a>
            <a href="#slide-15" class="w-6 h-6 flex items-center justify-center rounded-full hover:bg-dronexperto transition-colors text-xs">15</a>
            <a href="#slide-16" class="w-6 h-6 flex items-center justify-center rounded-full hover:bg-dronexperto transition-colors text-xs">16</a>
            <a href="#slide-17" class="w-6 h-6 flex items-center justify-center rounded-full hover:bg-dronexperto transition-colors text-xs">17</a>
            <a href="#slide-18" class="w-6 h-6 flex items-center justify-center rounded-full hover:bg-dronexperto transition-colors text-xs">18</a>
            <a href="#slide-19" class="w-6 h-6 flex items-center justify-center rounded-full hover:bg-dronexperto transition-colors text-xs">19</a>
            <a href="#slide-20" class="w-6 h-6 flex items-center justify-center rounded-full hover:bg-dronexperto transition-colors text-xs">20</a>
            <a href="#slide-21" class="w-6 h-6 flex items-center justify-center rounded-full hover:bg-dronexperto transition-colors text-xs">21</a>
            <a href="#slide-22" class="w-6 h-6 flex items-center justify-center rounded-full hover:bg-dronexperto transition-colors text-xs">22</a>
        </div>
    </footer>

    <!-- YouTube Lightbox HTML -->
    <div id="youtube-lightbox" class="lightbox hidden fixed inset-0 bg-black bg-opacity-90 flex justify-center items-center z-50 opacity-0 pointer-events-none">
        <div class="lightbox-content bg-black p-1 md:p-2 rounded-lg shadow-xl relative w-11/12 md:w-3/4 lg:w-2/3 transform scale-95">
            <button class="lightbox-close-btn absolute -top-3 -right-3 md:-top-4 md:-right-4 w-8 h-8 bg-white text-black rounded-full text-2xl font-bold flex items-center justify-center z-10">&times;</button>
            <div style="position: relative; padding-bottom: 56.25%; height: 0; border-radius: 0.5rem; overflow:hidden;">
                <iframe id="youtube-iframe" style="position: absolute; top: 0; left: 0; width: 100%; height: 100%;" src="" frameborder="0" allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture" allowfullscreen></iframe>
            </div>
        </div>
    </div>
    
    <!-- Image Lightbox HTML -->
    <div id="image-lightbox" class="lightbox hidden fixed inset-0 bg-black bg-opacity-90 flex justify-center items-center z-50 opacity-0 pointer-events-none">
        <div class="lightbox-content bg-black p-1 md:p-2 rounded-lg shadow-xl relative w-11/12 md:max-w-4xl transform scale-95">
            <button class="lightbox-close-btn absolute -top-3 -right-3 md:-top-4 md:-right-4 w-8 h-8 bg-white text-black rounded-full text-2xl font-bold flex items-center justify-center z-10">&times;</button>
            <img id="lightbox-image" src="" alt="Imagen Histórica" class="w-full h-auto object-contain max-h-[80vh] rounded-md">
        </div>
    </div>


    <script type="importmap">
        {
            "imports": {
                "three": "https://cdn.jsdelivr.net/npm/three@0.160.0/build/three.module.js"
            }
        }
    </script>
    <script src="https://player.vimeo.com/api/player.js"></script>
    <script type="module">
        import * as THREE from 'three';

        let scene, camera, renderer, stars, pyramids;

        function init() {
            // Scene
            scene = new THREE.Scene();

            // Camera
            camera = new THREE.PerspectiveCamera(60, window.innerWidth / window.innerHeight, 1, 1000);
            camera.position.z = 1;
            camera.rotation.x = Math.PI / 2;

            // Renderer
            renderer = new THREE.WebGLRenderer({
                canvas: document.querySelector('#bg-canvas'),
                antialias: true,
            });
            renderer.setSize(window.innerWidth, window.innerHeight);
            renderer.setPixelRatio(Math.min(window.devicePixelRatio, 2));

            // Stars
            const starGeometry = new THREE.BufferGeometry();
            const starVertices = [];
            for (let i = 0; i < 6000; i++) {
                const x = (Math.random() - 0.5) * 2000;
                const y = (Math.random() - 0.5) * 2000;
                const z = (Math.random() - 0.5) * 2000;
                starVertices.push(x, y, z);
            }
            starGeometry.setAttribute('position', new THREE.Float32BufferAttribute(starVertices, 3));
            const starMaterial = new THREE.PointsMaterial({ color: 0xaaaaaa, size: 0.7 });
            stars = new THREE.Points(starGeometry, starMaterial);
            scene.add(stars);

            // Pyramids (Tetrahedrons)
            const pyramidGeometry = new THREE.TetrahedronGeometry(1, 0);
            const pyramidMaterial = new THREE.MeshBasicMaterial({
                color: 0xdbfe0d, // dronexperto
                wireframe: true
            });
            pyramids = new THREE.Group();
            for (let i = 0; i < 500; i++) {
                const pyramid = new THREE.Mesh(pyramidGeometry, pyramidMaterial);
                pyramid.position.set(
                    (Math.random() - 0.5) * 200,
                    (Math.random() - 0.5) * 200,
                    (Math.random() - 0.5) * 200
                );
                pyramid.rotation.set(
                    Math.random() * Math.PI,
                    Math.random() * Math.PI,
                    Math.random() * Math.PI
                );
                const scale = Math.random() * 2 + 1;
                pyramid.scale.set(scale, scale, scale);
                pyramids.add(pyramid);
            }
            scene.add(pyramids);
            
            // Event Listeners
            window.addEventListener('resize', onWindowResize, false);
            document.querySelector('main').addEventListener('scroll', onScroll, false);

            animate();
        }

        function onWindowResize() {
            camera.aspect = window.innerWidth / window.innerHeight;
            camera.updateProjectionMatrix();
            renderer.setSize(window.innerWidth, window.innerHeight);
        }

        let scrollY = 0;
        function onScroll(e) {
            scrollY = e.target.scrollTop;
        }

        function animate() {
            requestAnimationFrame(animate);
            
            // Animate Stars
            stars.position.z += 0.1;
            if (stars.position.z > 1000) stars.position.z = -1000;
            
            // Animate pyramids based on scroll
            pyramids.rotation.y = scrollY * 0.0001;
            pyramids.rotation.x = scrollY * 0.0002;

            // Animate individual pyramids
            pyramids.children.forEach(p => {
                p.rotation.x += 0.001;
                p.rotation.y += 0.001;
            });

            renderer.render(scene, camera);
        }

        init();
        
        // --- YouTube Lightbox Logic ---
        const youtubeLightbox = document.getElementById('youtube-lightbox');
        const youtubeIframe = document.getElementById('youtube-iframe');

        function openYoutubeLightbox(videoId) {
            youtubeIframe.src = `https://www.youtube.com/embed/${videoId}?autoplay=1&rel=0`;
            showLightbox(youtubeLightbox);
        }
        window.openYoutubeLightbox = openYoutubeLightbox;

        // --- Image Lightbox Logic ---
        const imageLightbox = document.getElementById('image-lightbox');
        const lightboxImage = document.getElementById('lightbox-image');

        function openImageLightbox(imageUrl) {
            lightboxImage.src = imageUrl;
            showLightbox(imageLightbox);
        }
        window.openImageLightbox = openImageLightbox;
        
        // --- Generic Lightbox Functions ---
        function showLightbox(lightboxElement) {
            lightboxElement.classList.remove('hidden', 'pointer-events-none');
            setTimeout(() => {
                lightboxElement.classList.remove('opacity-0');
                lightboxElement.querySelector('.lightbox-content').classList.remove('scale-95');
            }, 10);
        }

        function closeLightbox(lightboxElement) {
            lightboxElement.classList.add('opacity-0');
            lightboxElement.querySelector('.lightbox-content').classList.add('scale-95');
            setTimeout(() => {
                lightboxElement.classList.add('hidden', 'pointer-events-none');
                const iframe = lightboxElement.querySelector('iframe');
                if (iframe) iframe.src = ''; // Stop video
            }, 300);
        }

        document.querySelectorAll('.lightbox').forEach(lb => {
            lb.addEventListener('click', (e) => {
                if (e.target === lb) {
                    closeLightbox(lb);
                }
            });
        });
        document.querySelectorAll('.lightbox-close-btn').forEach(btn => {
            btn.addEventListener('click', () => {
                closeLightbox(btn.closest('.lightbox'));
            });
        });
        document.addEventListener('keydown', (e) => {
            if (e.key === 'Escape') {
                document.querySelectorAll('.lightbox:not(.hidden)').forEach(closeLightbox);
            }
        });

    </script>
</body>
</html>

