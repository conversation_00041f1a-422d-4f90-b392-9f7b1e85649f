<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Dronexperto - Soluciones Profesionales con Drones</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;600;700;900&display=swap" rel="stylesheet">
    <style>
        /* Colores personalizados basados en tu identidad corporativa */
        :root {
            --dx-yellow: #dfff00; /* Un amarillo eléctrico/lima */
            --dx-black: #000000;
        }
        body {
            font-family: 'Inter', sans-serif;
            background-color: var(--dx-black);
            color: white;
        }
        /* Clases de utilidad para los colores */
        .bg-dx-yellow { background-color: var(--dx-yellow); }
        .text-dx-yellow { color: var(--dx-yellow); }
        .border-dx-yellow { border-color: var(--dx-yellow); }
        .focus\:border-dx-yellow:focus { border-color: var(--dx-yellow); }


        /* Canvas de Three.js como fondo */
        #bg-canvas {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            z-index: 0; /* Detrás del contenido pero visible */
        }

        /* Contenedor Parallax */
        #parallax-container {
            height: 300vh; /* Altura para dar espacio al scroll de la animación */
            position: relative;
            z-index: 2;
        }
        #parallax-canvas {
            position: sticky; /* Se pega mientras se hace scroll en el contenedor padre */
            top: 0;
            left: 0;
            width: 100%;
            height: 100vh;
        }

        /* Animación para el botón de llamada a la acción */
        .cta-button {
            transition: all 0.3s ease;
        }
        .cta-button:hover {
            transform: translateY(-3px);
            box-shadow: 0 10px 20px -10px var(--dx-yellow);
        }

        /* Contenedor del contenido para que se superponga al canvas */
        .content-wrapper {
            position: relative;
            width: 100%;
            z-index: 1;
        }

        /* Estilo para el select y options */
        select {
            -webkit-appearance: none;
            -moz-appearance: none;
            appearance: none;
            background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='%23dfff00' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3e%3cpolyline points='6 9 12 15 18 9'%3e%3c/polyline%3e%3c/svg%3e");
            background-repeat: no-repeat;
            background-position: right 1rem center;
            background-size: 1em;
        }
        select option {
            background: var(--dx-black);
            color: white;
        }
    </style>
</head>
<body class="antialiased">

<!-- Canvas para la animación de Three.js -->
<canvas id="bg-canvas"></canvas>

<div class="content-wrapper">
    <!-- Header -->
    <header class="bg-black/80 backdrop-blur-sm fixed w-full z-50 top-0">
        <div class="container mx-auto px-6 py-4 flex justify-between items-center">
            <div class="text-2xl font-black text-dx-yellow uppercase tracking-widest">Dronexperto</div>
            <nav class="hidden md:flex space-x-8">
                <a href="#servicios" class="hover:text-dx-yellow transition-colors">Servicios</a>
                <a href="#cursos" class="hover:text-dx-yellow transition-colors">Academia</a>
                <a href="#shows" class="hover:text-dx-yellow transition-colors">Shows</a>
                <a href="#venta" class="hover:text-dx-yellow transition-colors">Venta</a>
                <a href="#contacto" class="hover:text-dx-yellow transition-colors">Contacto</a>
            </nav>
            <button id="mobile-menu-button" class="md:hidden text-white">
                <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16m-7 6h7" /></svg>
            </button>
        </div>
        <div id="mobile-menu" class="hidden md:hidden px-6 pb-4">
            <a href="#servicios" class="block py-2 hover:text-dx-yellow">Servicios</a>
            <a href="#cursos" class="block py-2 hover:text-dx-yellow">Academia</a>
            <a href="#shows" class="block py-2 hover:text-dx-yellow">Shows</a>
            <a href="#venta" class="block py-2 hover:text-dx-yellow">Venta</a>
            <a href="#contacto" class="block py-2 hover:text-dx-yellow">Contacto</a>
        </div>
    </header>

    <!-- Contenido principal -->
    <main>
        <section id="hero" class="h-screen flex items-center justify-center relative">
            <div class="container mx-auto px-6 text-center z-10">
                <h1 class="text-4xl md:text-6xl lg:text-7xl font-black uppercase text-white leading-tight">Innovación que desafía la <span class="text-dx-yellow">gravedad.</span></h1>
                <p class="mt-4 text-lg md:text-xl text-gray-300 max-w-3xl mx-auto">En Dronexperto México S.A. de C.V. transformamos el cielo en un espacio de creatividad y tecnología. Con más de 6 años de experiencia en filmación FPV y más de 10 años innovando en la industria, ofrecemos filmación aérea, shows de drones, drones personalizados y formación académica, elevando tus ideas más allá de la imaginación y marcando tendencia en cada proyecto.</p>
                <a href="#contacto" class="cta-button mt-8 inline-block bg-dx-yellow text-black font-bold py-3 px-8 rounded-lg text-lg uppercase tracking-wider">Inicia tu Proyecto</a>
            </div>
        </section>

        <!-- SECCIÓN PARALLAX GSAP -->
        <section id="servicios" class="relative">
            <div id="parallax-container">
                <canvas id="parallax-canvas"></canvas>
                <div class="absolute inset-0 flex items-center justify-center">
                    <div class="bg-gray-900/80 backdrop-blur-sm rounded-lg p-8 md:p-12 border border-gray-800 max-w-4xl mx-4">
                        <h2 class="text-3xl font-black uppercase text-dx-yellow text-center">Filmación con Drones</h2>
                        <p class="mt-6 text-gray-300">En Dronexperto México transformamos la forma de ver y vivir los eventos a través de la filmación con drones FPV y drones personalizados de alto rendimiento. Nuestro servicio ofrece tomas únicas, dinámicas y precisas que no pueden lograrse con cámaras tradicionales.</p>
                        <div class="mt-6 grid md:grid-cols-2 gap-6 text-sm">
                            <div>
                                <h3 class="font-bold text-dx-yellow">Lo que nos distingue</h3>
                                <ul class="list-disc list-inside mt-2 text-gray-400 space-y-1">
                                    <li>Drones a medida para cada proyecto.</li>
                                    <li>Pilotos expertos en entornos complejos.</li>
                                    <li>Cobertura integral para eventos y producciones.</li>
                                    <li>Versatilidad creativa con FPV, tomas cenitales y más.</li>
                                </ul>
                            </div>
                            <div>
                                <h3 class="font-bold text-dx-yellow">Beneficios para tu proyecto</h3>
                                <ul class="list-disc list-inside mt-2 text-gray-400 space-y-1">
                                    <li>Generamos contenido audiovisual innovador.</li>
                                    <li>Logramos ángulos imposibles y secuencias inmersivas.</li>
                                    <li>Integramos postproducción y VFX para un producto final integral.</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- Contenedor para el resto de las tarjetas -->
        <div class="container mx-auto px-6 max-w-5xl space-y-96 py-24">

            <!-- Tarjeta de Fabricación -->
            <section class="bg-gray-900/80 backdrop-blur-sm rounded-lg p-8 md:p-12 border border-gray-800">
                <div class="grid md:grid-cols-2 gap-12 items-center">
                    <img src="https://placehold.co/600x400/dfff00/000000?text=Caja+Mágica+Voladora" alt="Dron personalizado 'Caja Mágica' volando" class="rounded-lg">
                    <div>
                        <h2 class="text-3xl font-black uppercase text-dx-yellow">Fabricación de Drones Personalizados</h2>
                        <p class="mt-4 text-gray-400">En Dronexperto México llevamos la innovación al siguiente nivel con nuestro servicio de fabricación de drones personalizados. Gracias a nuestra experiencia en diseño, ingeniería y producción, somos capaces de poner a volar prácticamente cualquier objeto que imagines.</p>
                        <div class="mt-6 grid md:grid-cols-2 gap-6 text-sm">
                            <div>
                                <h3 class="font-bold text-dx-yellow">Lo que ofrecemos</h3>
                                <ul class="list-disc list-inside mt-2 text-gray-400 space-y-1">
                                    <li>Diseños y vuelo de objetos a medida.</li>
                                    <li>"Cajas Mágicas" voladoras para branding.</li>
                                    <li>Innovación y seguridad con tecnología propia.</li>
                                </ul>
                            </div>
                            <div>
                                <h3 class="font-bold text-dx-yellow">Beneficios</h3>
                                <ul class="list-disc list-inside mt-2 text-gray-400 space-y-1">
                                    <li>Experiencias únicas para marketing.</li>
                                    <li>Diferenciación total frente a la competencia.</li>
                                    <li>Impacto visual garantizado en medios y redes.</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
            </section>

            <!-- Tarjeta de Academia -->
            <section id="cursos" class="bg-gray-900/80 backdrop-blur-sm rounded-lg p-8 md:p-12 border border-gray-800 text-center">
                <h2 class="text-3xl font-black uppercase">Academia <span class="text-dx-yellow">Dronexperto</span></h2>
                <p class="mt-4 text-lg text-gray-400 max-w-3xl mx-auto">En Dronexperto México formamos a la nueva generación de pilotos profesionales de drones. Nuestra academia ofrece cursos digitales y presenciales que van mucho más allá del simple vuelo: enseñamos normativa, seguridad aérea, filmación FPV, shows de drones, fabricación, mantenimiento y aplicaciones industriales.</p>
                <p class="mt-4 text-lg text-gray-400 max-w-3xl mx-auto">Convertirse en un Dronexperto es obtener un título de prestigio, un reconocimiento a la excelencia técnica, la creatividad y la responsabilidad en la operación de drones.</p>
                <div class="mt-10 grid md:grid-cols-2 gap-8 text-left">
                    <div>
                        <h3 class="font-bold text-xl text-dx-yellow mb-2">Lo que ofrecemos</h3>
                        <ul class="list-disc list-inside text-gray-400 space-y-2">
                            <li>Cursos digitales accesibles desde cualquier lugar.</li>
                            <li>Formación integral: pilotaje, normativa, seguridad, producción audiovisual, shows y aplicaciones industriales.</li>
                            <li>Opciones presenciales para práctica en campo con instructores expertos.</li>
                            <li>Certificación Dronexperto, respaldo de tu formación y habilidades.</li>
                        </ul>
                    </div>
                    <div>
                        <h3 class="font-bold text-xl text-dx-yellow mb-2">Beneficios</h3>
                        <ul class="list-disc list-inside text-gray-400 space-y-2">
                            <li>Aprende a tu ritmo y desde cualquier lugar.</li>
                            <li>Conviértete en parte de una élite creativa y técnica.</li>
                            <li>Obtén un título distintivo que te posiciona en la industria.</li>
                            <li>Crece con el respaldo de un equipo con más de 6 años de experiencia.</li>
                        </ul>
                    </div>
                </div>
            </section>

            <!-- Tarjeta de Shows -->
            <section id="shows" class="bg-gray-900/80 backdrop-blur-sm rounded-lg p-8 md:p-12 border border-gray-800">
                <div class="grid md:grid-cols-2 gap-12 items-center">
                    <img src="https://placehold.co/600x400/000000/dfff00?text=Show+de+Drones" alt="Show de luces con drones" class="rounded-lg">
                    <div>
                        <h2 class="text-3xl font-black uppercase text-dx-yellow">Show de Drones</h2>
                        <p class="mt-4 text-gray-400">En Dronexperto México transformamos el cielo en un lienzo con nuestros espectaculares shows de drones. A través de una coreografía perfectamente sincronizada, cientos de drones iluminados vuelan en conjunto para crear figuras, logotipos y animaciones que narran historias y transmiten emociones de una manera inolvidable.</p>
                        <div class="mt-6 grid md:grid-cols-2 gap-6 text-sm">
                            <div>
                                <h3 class="font-bold text-dx-yellow">Lo que ofrecemos</h3>
                                <ul class="list-disc list-inside mt-2 text-gray-400 space-y-1">
                                    <li>Espectáculos a gran escala (70 a 500+ drones).</li>
                                    <li>Diseños y coreografías personalizadas.</li>
                                    <li>Tecnología mexicana desarrollada en casa.</li>
                                    <li>Versatilidad para todo tipo de eventos masivos.</li>
                                </ul>
                            </div>
                            <div>
                                <h3 class="font-bold text-dx-yellow">Beneficios</h3>
                                <ul class="list-disc list-inside mt-2 text-gray-400 space-y-1">
                                    <li>Experiencia visual y emocional única.</li>
                                    <li>Contenido memorable para medios y redes.</li>
                                    <li>Formato innovador y sustentable.</li>
                                    <li>Combina arte, tecnología y branding.</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
            </section>

            <!-- Tarjeta de Venta -->
            <section id="venta" class="bg-gray-900/80 backdrop-blur-sm rounded-lg p-8 md:p-12 border border-gray-800">
                <div class="text-center mb-6">
                    <h2 class="text-3xl md:text-4xl font-black uppercase">Tienda <span class="text-dx-yellow">Dronexperto</span></h2>
                    <p class="mt-4 text-lg text-gray-400 max-w-3xl mx-auto">En la Tienda Dronexperto encontrarás todo lo que necesitas para llevar tu experiencia de vuelo al siguiente nivel. Desde drones FPV y equipos profesionales, hasta accesorios, repuestos, baterías y controladoras diseñadas para garantizar el mejor rendimiento.</p>
                </div>
                <div class="grid md:grid-cols-2 gap-8 text-left">
                    <div>
                        <h3 class="font-bold text-xl text-dx-yellow mb-2">Lo que ofrecemos</h3>
                        <ul class="list-disc list-inside text-gray-400 space-y-2">
                            <li>Drones personalizados y FPV listos para volar.</li>
                            <li>Accesorios y repuestos originales para mantener tu dron siempre en óptimas condiciones.</li>
                            <li>Tecnología propia Dronexperto, innovadora y segura.</li>
                            <li>Soporte especializado para ayudarte a elegir el equipo ideal según tus necesidades.</li>
                        </ul>
                    </div>
                    <div>
                        <h3 class="font-bold text-xl text-dx-yellow mb-2">Beneficios</h3>
                        <ul class="list-disc list-inside text-gray-400 space-y-2">
                            <li>Compra con la confianza de una empresa con más de 6 años de experiencia.</li>
                            <li>Accede a tecnología mexicana de vanguardia en drones y componentes.</li>
                            <li>Encuentra todo en un solo lugar: desde iniciación hasta nivel profesional.</li>
                            <li>Respaldo y garantía de Dronexperto México.</li>
                        </ul>
                    </div>
                </div>
            </section>

            <!-- Tarjeta de Contacto -->
            <section id="contacto" class="bg-gray-900/80 backdrop-blur-sm rounded-lg p-8 md:p-12 border border-gray-800">
                <div class="text-center">
                    <h2 class="text-3xl md:text-4xl font-black uppercase">Hablemos de tu <span class="text-dx-yellow">Próximo Desafío</span></h2>
                </div>
                <div class="max-w-xl mx-auto mt-8">
                    <form action="#" method="POST" class="space-y-6">
                        <div><input type="text" placeholder="Tu Nombre Completo" class="w-full bg-black border-2 border-gray-700 rounded-md py-3 px-4 text-white focus:border-dx-yellow focus:outline-none transition-colors"></div>
                        <div><input type="email" placeholder="Tu Correo Electrónico" class="w-full bg-black border-2 border-gray-700 rounded-md py-3 px-4 text-white focus:border-dx-yellow focus:outline-none transition-colors"></div>
                        <div>
                            <select class="w-full bg-black border-2 border-gray-700 rounded-md py-3 px-4 text-white focus:border-dx-yellow focus:outline-none transition-colors">
                                <option disabled selected>Selecciona un servicio</option>
                                <option>Filmación con Drones</option>
                                <option>Fabricación de Drones Personalizados</option>
                                <option>Show de Drones</option>
                                <option>Academia Dronexperto</option>
                                <option>Tienda Dronexperto</option>
                                <option>Otro</option>
                            </select>
                        </div>
                        <div><textarea rows="4" placeholder="Describe tu proyecto..." class="w-full bg-black border-2 border-gray-700 rounded-md py-3 px-4 text-white focus:border-dx-yellow focus:outline-none transition-colors"></textarea></div>
                        <div><button type="submit" class="cta-button w-full bg-dx-yellow text-black font-bold py-3 px-8 rounded-lg text-lg uppercase tracking-wider">Enviar Mensaje</button></div>
                    </form>
                </div>
            </section>

        </div>
    </main>

    <!-- Footer -->
    <footer class="bg-black py-8 relative z-10 mt-24">
        <div class="container mx-auto px-6 text-center text-gray-500">
            <p>&copy; 2025 Dronexperto. Todos los derechos reservados.</p>
            <div class="flex justify-center space-x-6 mt-4">
                <a href="#" class="hover:text-white">Facebook</a>
                <a href="#" class="hover:text-white">Instagram</a>
                <a href="#" class="hover:text-white">YouTube</a>
            </div>
        </div>
    </footer>
</div>


<!-- Scripts -->
<script src="https://cdnjs.cloudflare.com/ajax/libs/three.js/r128/three.min.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/gsap/3.9.1/gsap.min.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/gsap/3.9.1/ScrollTrigger.min.js"></script>
<script>
    // --- SCRIPT MENÚ MÓVIL ---
    document.getElementById('mobile-menu-button').addEventListener('click', () => {
        document.getElementById('mobile-menu').classList.toggle('hidden');
    });

    // --- SCRIPT GSAP PARALLAX ---
    gsap.registerPlugin(ScrollTrigger);

    const parallaxCanvas = document.getElementById('parallax-canvas');
    const context = parallaxCanvas.getContext('2d');

    parallaxCanvas.width = 1920;
    parallaxCanvas.height = 1080;

    const frameCount = 120; // Número de imágenes en la secuencia
    const currentFrame = index => (
        // Usamos placehold.co para simular la secuencia de imágenes
        `https://placehold.co/1920x1080/000000/111111?text=Frame+${index.toString().padStart(3, '0')}`
    );

    const images = [];
    const imageSeq = {
        frame: 0
    };

    for (let i = 0; i < frameCount; i++) {
        const img = new Image();
        img.src = currentFrame(i + 1);
        images.push(img);
    }

    gsap.to(imageSeq, {
        frame: frameCount - 1,
        snap: 'frame',
        ease: 'none',
        scrollTrigger: {
            trigger: '#parallax-container',
            start: 'top top',
            end: 'bottom bottom',
            scrub: 0.5,
        },
        onUpdate: render
    });

    images[0].onload = render;

    function render() {
        context.clearRect(0, 0, parallaxCanvas.width, parallaxCanvas.height);
        context.drawImage(images[imageSeq.frame], 0, 0);
    }


    // --- SCRIPT THREE.JS ---
    window.addEventListener('DOMContentLoaded', () => {
        let scene, camera, renderer;
        const objectGroups = [];
        const sectionCount = 6;
        const distanceBetweenGroups = 140; // Aumentamos la distancia para el nuevo layout
        let targetCameraZ = 0;
        const mouse = new THREE.Vector2();

        const colors = [
            new THREE.Color(0xdfff00), // hero
            new THREE.Color(0xdfff00), // servicios
            new THREE.Color(0xdfff00), // cursos
            new THREE.Color(0xdfff00), // shows
            new THREE.Color(0xdfff00), // venta
            new THREE.Color(0xffffff)  // contacto
        ];

        function init() {
            scene = new THREE.Scene();
            camera = new THREE.PerspectiveCamera(75, window.innerWidth / window.innerHeight, 0.1, 1000);
            camera.position.z = 5;

            const canvas = document.getElementById('bg-canvas');
            renderer = new THREE.WebGLRenderer({ canvas: canvas, alpha: true });
            renderer.setSize(window.innerWidth, window.innerHeight);

            const ambientLight = new THREE.AmbientLight(0xffffff, 0.5);
            scene.add(ambientLight);
            const directionalLight = new THREE.DirectionalLight(0xffffff, 0.5);
            directionalLight.position.set(5, 5, 5);
            scene.add(directionalLight);

            for (let i = 0; i < sectionCount; i++) {
                const group = createObjectGroup(colors[i % colors.length]);
                group.position.z = -i * distanceBetweenGroups;
                scene.add(group);
                objectGroups.push(group);
            }

            window.addEventListener('resize', onWindowResize, false);
            document.addEventListener('scroll', onScroll, false);
            document.addEventListener('mousemove', onMouseMove, false);

            animate();
        }

        function createObjectGroup(color) {
            const group = new THREE.Group();
            const pyramidGeometry = new THREE.TetrahedronGeometry(1);

            for (let i = 0; i < 25; i++) {
                const material = new THREE.MeshStandardMaterial({ color: color, roughness: 0.5, metalness: 0.5 });
                const mesh = new THREE.Mesh(pyramidGeometry, material);
                mesh.position.x = (Math.random() - 0.5) * 20;
                mesh.position.y = (Math.random() - 0.5) * 20;
                mesh.position.z = (Math.random() - 0.5) * 10;
                mesh.rotation.x = Math.random() * 2 * Math.PI;
                mesh.rotation.y = Math.random() * 2 * Math.PI;
                mesh.rotation.z = Math.random() * 2 * Math.PI;
                const scale = Math.random() * 2.5 + 0.1; // Tamaños más aleatorios
                mesh.scale.set(scale, scale, scale);
                group.add(mesh);
            }

            const starCount = 300;
            const starPositions = new Float32Array(starCount * 3\\for (let i = 0; i < starCount; i++)  LNOP{
                \
]0
                0P_|
                ;.starPositions[i * 3 + 0] = (Math.random() - 0.5) * 30;\0-,lp8=]'' +
                '/starPositions[i * 3 + 1] = (Math.random() - 0.5) * 30;
                starPositions[i * 3 + 2] = (Math.random() - 0.5) * 25;
            }
            const starGeometry = new THREE.BufferGeometry();
            starGeometry.setAttribute('position', new THREE.BufferAttribute(starPositions, 3));
            const starMaterial = new THREE.PointsMaterial({ color: 0xffffff, size: 0.05, transparent: true, opacity: 0.8 });
            const stars = new THREE.Points(starGeometry, starMaterial);
            group.add(stars);

            return group;
        }

        function onMouseMove(event) {
            mouse.x = (event.clientX / window.innerWidth) * 2 - 1;
            mouse.y = - (event.clientY / window.innerHeight) * 2 + 1;
        }

        function animate() {
            requestAnimationFrame(animate);
            objectGroups.forEach(group => {
                group.rotation.x += 0.0005;
                group.rotation.y += 0.001;
            });

            // Movimiento de cámara con el scroll
            camera.position.z += (targetCameraZ - camera.position.z) * 0.05;

            // Movimiento sutil de la cámara con el mouse
            const parallaxX = mouse.x * 1.5; // Movimiento intensificado
            const parallaxY = mouse.y * 1.5; // Movimiento intensificado
            camera.position.x += (parallaxX - camera.position.x) * 0.05;
            camera.position.y += (parallaxY - camera.position.y) * 0.05;


            renderer.render(scene, camera);
        }

        function onWindowResize() {
            camera.aspect = window.innerWidth / window.innerHeight;
            camera.updateProjectionMatrix();
            renderer.setSize(window.innerWidth, window.innerHeight);
        }

        function onScroll() {
            const scrollableHeight = document.querySelector('.content-wrapper').scrollHeight - window.innerHeight;
            const scrollPercent = window.scrollY / scrollableHeight;
            const totalZMovement = (sectionCount - 1) * distanceBetweenGroups;
            targetCameraZ = 5 - (scrollPercent * totalZMovement);
        }

        init();
    });
</script>
</body>
</html>
