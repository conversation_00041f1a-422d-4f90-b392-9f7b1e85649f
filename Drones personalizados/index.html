<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Presentación de Drones Personalizados</title>
    <style>
        /* Estilos generales y para el entorno 3D */
        body {
            margin: 0;
            padding: 0;
            overflow: hidden; /* Muy importante para evitar dobles barras de scroll */
            background-color: #02000f;
            font-family: 'Inter', system-ui, sans-serif;
            color: #fff;
        }

        /* Contenedor principal para superponer los lienzos */
        #container {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
        }

        /* Lienzo para el renderizador CSS3D (nuestras diapositivas HTML) */
        #css-renderer {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
        }
        
        /* Lienzo para el renderizador WebGL (estrellas y efectos) */
        #webgl-canvas {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            pointer-events: none; /* Permite interactuar con los elementos HTML de abajo */
        }

        /* Estilo de las "diapositivas" o secciones de contenido */
        .section-card {
            width: 600px;
            max-width: 90vw;
            height: 550px; /* Altura aumentada para video y botón */
            background: rgba(0, 0, 0, 0.75);
            border: 1px solid rgba(255, 255, 255, 0.1);
            border-radius: 20px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.5);
            backdrop-filter: blur(10px);
            -webkit-backdrop-filter: blur(10px);
            padding: 30px;
            box-sizing: border-box;
            text-align: center;
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            transition: opacity 0.1s ease-out;
        }

        .section-card h2 {
            font-size: 2.2em; /* Ligeramente ajustado */
            margin-top: 0;
            color: #C0FF00;
            text-shadow: 0 0 10px #C0FF00;
        }
        
        .section-card iframe {
            width: 100%;
            height: 240px; /* Altura fija para el video */
            border-radius: 10px;
            margin: 15px 0; /* Espacio arriba y abajo */
            border: none;
        }

        .section-card p {
            font-size: 1.1em; /* Ligeramente más pequeño para dar espacio */
            line-height: 1.5;
            color: #cbd5e1;
        }
        
        .cta-button {
            margin-top: auto; /* Empuja el botón hacia abajo */
            padding: 10px 25px;
            background-color: transparent;
            border: 2px solid #C0FF00;
            color: #C0FF00;
            border-radius: 50px;
            cursor: pointer;
            font-size: 0.9em;
            font-weight: bold;
            text-transform: uppercase;
            transition: all 0.3s ease;
            pointer-events: auto; /* Asegura que el botón sea clickeable */
        }

        .cta-button:hover {
            background-color: #C0FF00;
            color: #02000f;
            box-shadow: 0 0 15px #C0FF00;
        }

        /* Indicador visual para que el usuario sepa que debe hacer scroll */
        #scroll-hint {
            position: fixed;
            bottom: 20px;
            left: 50%;
            transform: translateX(-50%);
            color: white;
            z-index: 100;
            font-size: 0.9em;
            opacity: 0.8;
            animation: pulse 2s infinite;
        }

        @keyframes pulse {
            0% { opacity: 0.8; }
            50% { opacity: 0.4; }
            100% { opacity: 0.8; }
        }
    </style>
</head>
<body>
    <div id="container">
        <!-- Aquí se montará el renderizador de CSS3D -->
        <div id="css-renderer"></div>
        <!-- El canvas para el renderizador de WebGL se creará con JS -->
    </div>

    <div id="scroll-hint">
        Desliza para navegar
    </div>

    <!-- Import Map para Three.js y sus módulos adicionales -->
    <script type="importmap">
        {
            "imports": {
                "three": "https://cdn.jsdelivr.net/npm/three@0.164.1/build/three.module.js",
                "three/addons/": "https://cdn.jsdelivr.net/npm/three@0.164.1/examples/jsm/"
            }
        }
    </script>

    <script type="module">
        import * as THREE from 'three';
        import { CSS3DRenderer, CSS3DObject } from 'three/addons/renderers/CSS3DRenderer.js';

        // --- Variables globales ---
        let scene, camera;
        let webglRenderer, cssRenderer;
        let curve;
        let scrollPercent = 0;
        let targetScroll = 0;
        let touchStartY = 0;
        const objects = [];
        const pyramids = [];
        let stars; // Variable para acceder a las estrellas globalmente

        // --- CONTENIDO DE LA PRESENTACIÓN ---
        const servicesContent = [
            {
                title: "Drones Personalizados",
                description: "Soluciones aéreas a la medida de tu imaginación. Creamos tecnología de vuelo única para proyectos espectaculares.",
                position: new THREE.Vector3(0, 0, 0)
            },
            {
                title: "Fabricación a la Medida",
                description: "Desde el concepto hasta el vuelo. Diseñamos y construimos drones personalizados para cumplir con los requerimientos técnicos y visuales de tu proyecto.",
                position: new THREE.Vector3(-5, 2, -1500)
            },
            {
                title: "Cajas Mágicas Voladoras",
                description: "Empaques de marca que vuelan. Creamos un sistema de propulsión seguro y a la medida para hacer volar cajas y productos.",
                position: new THREE.Vector3(0, 0, -3000),
                videoId: "eb7139eed97ccacbfdd158982ce10890"
            },
            {
                title: "Entrega Aérea de Objetos",
                description: "Tecnología de precisión para entregas seguras. Ideal para trofeos en premiaciones, productos en eventos y más.",
                position: new THREE.Vector3(-10, 5, -4500),
                videoId: "ad58d0e92faa4db59a71f9d75a0c3765"
            },
            {
                title: "Drone Repartidor de Cupones",
                description: "Un sistema innovador que libera cupones desde el aire. Sorprende a tu público y dirige el tráfico a tu negocio.",
                position: new THREE.Vector3(15, -5, -6000),
                videoId: "69299cb3c20a69f11d5b0d73f53a8c60"
            },
            {
                title: "Drones Caracterizados",
                description: "Damos vida a tus personajes para comerciales, películas y eventos. Creamos Drones Paloma, Águila, Espectro, etc.",
                position: new THREE.Vector3(0, 0, -7500),
                videoId: "8aea212b32eb991f0e1e90d2ba8941a8"
            },
            {
                title: "Tu Visión, Nuestro Vuelo",
                description: "Cuéntanos tu idea más audaz. Nosotros nos encargamos de la ingeniería para hacerla volar.",
                position: new THREE.Vector3(10, 0, -9000)
            }
        ];

        // --- Función de inicialización ---
        function init() {
            scene = new THREE.Scene();
            camera = new THREE.PerspectiveCamera(75, window.innerWidth / window.innerHeight, 0.1, 10000);
            
            const ambientLight = new THREE.AmbientLight(0xffffff, 0.6);
            scene.add(ambientLight);
            const directionalLight = new THREE.DirectionalLight(0xffffff, 1.0);
            directionalLight.position.set(5, 10, 7);
            scene.add(directionalLight);

            webglRenderer = new THREE.WebGLRenderer({ antialias: true, alpha: true });
            webglRenderer.setSize(window.innerWidth, window.innerHeight);
            webglRenderer.domElement.id = 'webgl-canvas';
            document.getElementById('container').appendChild(webglRenderer.domElement);

            cssRenderer = new CSS3DRenderer();
            cssRenderer.setSize(window.innerWidth, window.innerHeight);
            document.getElementById('css-renderer').appendChild(cssRenderer.domElement);
            
            createStars();
            createFloatingPyramids();
            createContentSections();
            createCameraPath();

            window.addEventListener('resize', onWindowResize);
            window.addEventListener('wheel', onMouseWheel);
            window.addEventListener('touchstart', onTouchStart, { passive: false });
            window.addEventListener('touchmove', onTouchMove, { passive: false });

            animate();
        }

        function createStars() {
            const starGeometry = new THREE.BufferGeometry();
            const starVertices = [];
            // Se reduce la dispersión, ya que las estrellas ahora seguirán a la cámara
            for (let i = 0; i < 25000; i++) {
                const x = THREE.MathUtils.randFloatSpread(4000);
                const y = THREE.MathUtils.randFloatSpread(4000);
                const z = THREE.MathUtils.randFloatSpread(4000);
                starVertices.push(x, y, z);
            }
            starGeometry.setAttribute('position', new THREE.Float32BufferAttribute(starVertices, 3));
            const starMaterial = new THREE.PointsMaterial({ color: 0xffffff, size: 0.9 });
            stars = new THREE.Points(starGeometry, starMaterial); // Se asigna a la variable global
            scene.add(stars);
        }

        function createFloatingPyramids() {
            const geometry = new THREE.ConeGeometry(10, 15, 4);
            const material = new THREE.MeshStandardMaterial({
                color: 0xC0FF00,
                metalness: 0.4,
                roughness: 0.5
            });

            for (let i = 0; i < 200; i++) {
                const pyramid = new THREE.Mesh(geometry, material);

                const x = THREE.MathUtils.randFloatSpread(1200);
                const y = THREE.MathUtils.randFloatSpread(1200);
                const z = THREE.MathUtils.randFloat(-9500, 500); // Rango extendido
                pyramid.position.set(x, y, z);
                
                pyramid.rotation.set(Math.random() * Math.PI, Math.random() * Math.PI, Math.random() * Math.PI);

                const scale = THREE.MathUtils.randFloat(0.5, 2.0);
                pyramid.scale.set(scale, scale, scale);

                pyramid.userData.rotationSpeed = { x: (Math.random() - 0.5) * 0.015, y: (Math.random() - 0.5) * 0.015, z: (Math.random() - 0.5) * 0.015 };

                scene.add(pyramid);
                pyramids.push(pyramid);
            }
        }

        // --- FUNCIÓN CORREGIDA ---
        function createContentSections() {
            servicesContent.forEach(service => {
                const element = document.createElement('div');
                element.className = 'section-card';

                // Crear y añadir el título
                const titleEl = document.createElement('h2');
                titleEl.textContent = service.title;
                element.appendChild(titleEl);

                // Crear y añadir el video si existe
                if (service.videoId) {
                    const iframeEl = document.createElement('iframe');
                    // --- URL CORREGIDA ---
                    iframeEl.src = `https://iframe.videodelivery.net/${service.videoId}?autoplay=false&muted=true`;
                    iframeEl.loading = 'lazy';
                    iframeEl.style.border = 'none';
                    iframeEl.setAttribute('allow', 'accelerometer; gyroscope; autoplay; encrypted-media; picture-in-picture;');
                    iframeEl.setAttribute('allowfullscreen', 'true');
                    element.appendChild(iframeEl);
                }

                // Crear y añadir la descripción
                const descEl = document.createElement('p');
                descEl.textContent = service.description;
                element.appendChild(descEl);

                // Crear y añadir el botón si existe video
                if (service.videoId) {
                    const buttonEl = document.createElement('button');
                    buttonEl.className = 'cta-button';
                    buttonEl.textContent = 'Ver más';
                    element.appendChild(buttonEl);
                }

                const object = new CSS3DObject(element);
                object.position.copy(service.position);
                scene.add(object);
                objects.push(object);
            });
        }

        function createCameraPath() {
            curve = new THREE.CatmullRomCurve3([
                new THREE.Vector3(0, 0, 500),
                ...servicesContent.map(s => s.position),
                new THREE.Vector3(10, 0, -9050) // Punto final ajustado
            ]);
        }

        function onMouseWheel(event) {
            const delta = event.deltaY * 0.0002;
            targetScroll += delta;
            targetScroll = Math.max(0, Math.min(1, targetScroll));
        }

        function onTouchStart(event) {
            event.preventDefault();
            touchStartY = event.touches[0].clientY;
        }

        function onTouchMove(event) {
            event.preventDefault();
            const touchY = event.touches[0].clientY;
            const deltaY = touchStartY - touchY;
            touchStartY = touchY;
            const delta = deltaY * 0.0008;
            targetScroll += delta;
            targetScroll = Math.max(0, Math.min(1, targetScroll));
        }

        function onWindowResize() {
            camera.aspect = window.innerWidth / window.innerHeight;
            camera.updateProjectionMatrix();
            webglRenderer.setSize(window.innerWidth, window.innerHeight);
            cssRenderer.setSize(window.innerWidth, window.innerHeight);
        }

        function animate() {
            requestAnimationFrame(animate);
            
            scrollPercent = THREE.MathUtils.lerp(scrollPercent, targetScroll, 0.07);
            
            const cameraPos = curve.getPointAt(scrollPercent);
            const lookAtPos = curve.getPointAt(Math.min(scrollPercent + 0.01, 1));
            camera.position.copy(cameraPos);
            camera.lookAt(lookAtPos);
            
            // --- LAS ESTRELLAS AHORA SIGUEN A LA CÁMARA ---
            if (stars) {
                stars.position.copy(camera.position);
            }

            objects.forEach(obj => {
                obj.lookAt(camera.position);

                const distance = camera.position.distanceTo(obj.position);
                const fadeOutStartDistance = 400;
                const fadeOutEndDistance = 150;

                let opacity = 1.0;

                if (distance < fadeOutStartDistance) {
                    const progress = (distance - fadeOutEndDistance) / (fadeOutStartDistance - fadeOutEndDistance);
                    opacity = Math.max(0, Math.min(1, progress));
                }
                
                obj.element.style.opacity = opacity;
            });

            pyramids.forEach(p => {
                p.rotation.x += p.userData.rotationSpeed.x;
                p.rotation.y += p.userData.rotationSpeed.y;
                p.rotation.z += p.userData.rotationSpeed.z;
            });
            
            webglRenderer.render(scene, camera);
            cssRenderer.render(scene, camera);
        }
        
        init();
    </script>
</body>
</html>

