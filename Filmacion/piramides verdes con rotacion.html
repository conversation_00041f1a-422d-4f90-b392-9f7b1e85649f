<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Dronexperto - Cinematografía Aérea Profesional</title>

    <!-- Meta tags para SEO -->
    <meta name="description" content="Filmación aérea profesional con drones en México. Creamos experiencias visuales únicas para cine, TV, eventos y producciones comerciales. Tecnología propia, pilotos certificados y postproducción profesional.">
    <meta name="keywords" content="drones, filmación aérea, cinematografía, México, FPV, producción audiovisual, eventos, cine, televisión, comerciales">
    <meta name="author" content="Dronexperto México">

    <!-- Open Graph tags para WhatsApp, Facebook, etc. -->
    <meta property="og:title" content="Dronexperto - Filmación Aérea Profesional">
    <meta property="og:description" content="Llevamos tus ideas a nuevas alturas. Filmación aérea profesional con drones, tecnología propia y más de 6 años de experiencia en México.">
    <meta property="og:image" content="https://dronexperto.com/filmacion-con-drones/images/dronexperto_og_image.jpg">
    <meta property="og:image:width" content="1200">
    <meta property="og:image:height" content="630">
    <meta property="og:url" content="https://dronexperto.com/filmacion-con-drones/">
    <meta property="og:type" content="website">
    <meta property="og:site_name" content="Dronexperto México S.A. de C.V.">
    <meta property="og:locale" content="es_MX">

    <!-- Twitter Card tags -->
    <meta name="twitter:card" content="summary_large_image">
    <meta name="twitter:title" content="Dronexperto - Filmación Aérea Profesional">
    <meta name="twitter:description" content="Filmación aérea profesional con drones en México. Tecnología propia, pilotos certificados y experiencias visuales únicas.">
    <meta name="twitter:image" content="https://dronexperto.com/filmacion-con-drones/images/dronexperto_og_image.jpg">

    <!-- WhatsApp específico -->
    <meta property="og:image:alt" content="Dronexperto - Filmación aérea profesional con drones en México">

    <!-- Favicon -->
    <link rel="icon" type="image/x-icon" href="https://dronexperto.com/filmacion-con-drones/images/favicon.ico">
    <link rel="apple-touch-icon" href="https://dronexperto.com/filmacion-con-drones/images/apple-touch-icon.png">
    <style>
        :root {
            --neon-green: #dbfe0d;
            --purple: #8A2BE2;
            --white: #FFFFFF;
            --black: #000000;
        }

        body {
            background-color: var(--black);
            color: var(--white);
            margin: 0;
            padding: 0;
            font-family: 'Inter', 'Helvetica Neue', Helvetica, Arial, sans-serif;
            overflow-x: hidden;
        }

        /* Contenedores para los renderers de Three.js */
        .three-container {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100vh;
            outline: none;
            pointer-events: none;
        }

        #three-canvas {
            z-index: 1;
        }

        #css-renderer {
            pointer-events: auto;
            z-index: 3;
        }

        /* Contenedor que genera la barra de scroll */
        .scroll-container {
            position: relative;
            width: 100%;
            height: 4500vh;
            z-index: 1;
        }

        #content-source {
            display: none;
        }

        .content-section {
            width: 800px;
            pointer-events: auto;
        }

        /* Efecto de flotación puro */
        .text-box {
            max-width: 600px;
            margin: 0 auto;
            padding: 1rem;
            text-align: center;
            background: transparent;
            border: none;
            transition: background-color 0.5s ease;
        }

        /* Card de introducción con fondo negro */
        #intro-card .text-box {
            background-color: var(--black);
        }

        /* Títulos con efecto 3D nítido y brillo sutil */
        .text-box h1, .text-box h2 {
            font-size: 2.8rem;
            font-weight: bold;
            color: var(--neon-green);
            margin-bottom: 1.5rem;
            text-shadow:
                    0 0 10px rgba(219, 254, 13, 0.7),
                    0 1px 0 #849907,
                    0 2px 0 #849907,
                    0 3px 0 #849907;
        }

        .text-box p {
            font-size: 1.2rem;
            line-height: 1.7;
            text-shadow: 0 0 8px rgba(0,0,0,0.8);
        }

        .highlight {
            color: var(--neon-green);
            font-weight: bold;
        }

        #logo-container {
            max-width: 250px;
            margin: 0 auto 1rem;
        }

        #logo-container img {
            width: 100%;
            height: auto;
            filter: drop-shadow(0 0 15px var(--neon-green));
        }

        .video-placeholder {
            width: 100%;
            padding-top: 56.25%;
            position: relative;
            background: transparent;
            border-radius: 0.5rem;
            margin-top: 1.5rem;
            border: none;
            display: flex;
            align-items: center;
            justify-content: center;
            overflow: hidden;
        }

        .video-placeholder iframe, .video-placeholder .video-thumbnail {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            border-radius: 1rem;
            border: none;
        }

        .video-thumbnail {
            object-fit: cover;
            z-index: 2;
            transition: opacity 0.5s ease-out;
        }

        .video-placeholder.loaded .video-thumbnail {
            opacity: 0;
            pointer-events: none;
        }


        #jukebox-section {
            position: relative;
            padding: 10vh 5%;
            text-align: center;
            background-color: var(--black);
            z-index: 2;
        }

        .jukebox-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
            gap: 1.5rem;
            margin-top: 2rem;
        }

        .video-card {
            background: #111;
            border-radius: 1rem;
            overflow: hidden;
            cursor: pointer;
            transition: transform 0.3s ease, box-shadow 0.3s ease;
            border: 1px solid #333;
        }

        .video-card:hover {
            transform: scale(1.05);
            box-shadow: 0 0 20px var(--neon-green);
        }

        .video-card img {
            width: 100%;
            height: 180px;
            object-fit: cover;
            display: block;
        }

        .video-card-title {
            padding: 1rem;
            font-size: 1rem;
            font-weight: bold;
            color: var(--white);
        }

        #video-modal {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.9);
            display: none;
            justify-content: center;
            align-items: center;
            z-index: 1000;
        }

        .modal-content {
            position: relative;
            width: 90%;
            max-width: 960px;
            padding-top: 56.25%;
        }

        .modal-content iframe {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            border: none;
        }

        .close-modal {
            position: absolute;
            top: 20px;
            right: 30px;
            font-size: 3rem;
            color: var(--white);
            cursor: pointer;
            transition: color 0.3s ease;
        }

        .close-modal:hover {
            color: var(--neon-green);
        }

        @media (max-width: 768px) {
            .text-box h1, .text-box h2 { font-size: 2rem; }
            .text-box p { font-size: 1rem; }
            .content-section { width: 90vw; }
        }
    </style>
</head>
<body>
<canvas id="three-canvas" class="three-container"></canvas>
<div id="css-renderer" class="three-container"></div>

<div class="scroll-container"></div>


<div id="content-source">

    <!-- Slide 1: Intro con ID para el card -->
    <div id="intro-card" class="content-section">
        <div class="text-box">
            <div id="logo-container">
                <img src="https://dronexperto.com/filmacion-con-drones/images/dronexperto_logo_neon.svg" alt="Logo Dronexperto Neon">
            </div>
            <h1>Filmación Aérea Profesional con Drones</h1>
            <p>En Dronexperto México creemos que cada historia merece ser contada desde la mejor perspectiva. Nuestros drones no solo vuelan, crean emociones. Diseñamos y fabricamos cada uno a la medida de tu proyecto, pilotados con precisión y respaldados por años de experiencia para lograr tomas únicas y seguras. Ya sea en la intensidad de un concierto, la fuerza de un evento corporativo o la magia de una producción cinematográfica, llevamos tus ideas a nuevas alturas. Con postproducción y VFX, convertimos cada imagen en una experiencia visual que impacta y conecta con tu audiencia.</p>
        </div>
    </div>


    <!-- Slide 2 -->
    <div class="content-section">
        <div class="text-box">
            <h2>Una Nueva Perspectiva</h2>
            <p>Vemos el mundo desde ángulos que pocos pueden alcanzar. Creamos imágenes aéreas que convierten cualquier proyecto en una experiencia inmersiva.</p>
            <div class="video-placeholder" data-cloudflare-id="1278846ddcf1ee86e90ef589a63e9a96">
                <img src="https://videodelivery.net/1278846ddcf1ee86e90ef589a63e9a96/thumbnails/thumbnail.jpg" class="video-thumbnail" alt="Thumbnail">
                <iframe class="cloudflare-video" src="https://iframe.videodelivery.net/1278846ddcf1ee86e90ef589a63e9a96?autoplay=true&muted=true&loop=true&controls=false" allow="accelerometer; gyroscope; autoplay; encrypted-media; picture-in-picture;" allowfullscreen="true"></iframe>
            </div>
        </div>
    </div>
    <!-- Slide 3 -->
    <div class="content-section">
        <div class="text-box">
            <h2>Contamos Historias desde el Aire</h2>
            <p>No es solo grabar: es narrar tu historia con emoción y creatividad, desde un punto de vista único.</p>
            <div class="video-placeholder" data-cloudflare-id="69299cb3c20a69f11d5b0d73f53a8c60">
                <img src="https://videodelivery.net/69299cb3c20a69f11d5b0d73f53a8c60/thumbnails/thumbnail.jpg" class="video-thumbnail" alt="Thumbnail">
                <iframe class="cloudflare-video" src="https://iframe.videodelivery.net/69299cb3c20a69f11d5b0d73f53a8c60?autoplay=true&muted=true&loop=true&controls=false" allow="accelerometer; gyroscope; autoplay; encrypted-media; picture-in-picture;" allowfullscreen="true"></iframe>
            </div>
        </div>
    </div>
    <!-- Slide 4 -->
    <div class="content-section">
        <div class="text-box">
            <h2>Tecnología Propia</h2>
            <p>Diseñamos y fabricamos nuestros drones para lograr tomas imposibles con la máxima seguridad.</p>
            <div class="video-placeholder" data-cloudflare-id="8aea212b32eb991f0e1e90d2ba8941a8">
                <img src="https://videodelivery.net/8aea212b32eb991f0e1e90d2ba8941a8/thumbnails/thumbnail.jpg" class="video-thumbnail" alt="Thumbnail">
                <iframe class="cloudflare-video" src="https://iframe.videodelivery.net/8aea212b32eb991f0e1e90d2ba8941a8?autoplay=true&muted=true&loop=true&controls=false" allow="accelerometer; gyroscope; autoplay; encrypted-media; picture-in-picture;" allowfullscreen="true"></iframe>
            </div>
        </div>
    </div>
    <!-- Slide 5 -->
    <div class="content-section">
        <div class="text-box">
            <h2>Precisión Profesional</h2>
            <p>Pilotos altamente capacitados que vuelan con exactitud milimétrica en entornos complejos y espacios reducidos.</p>
            <div class="video-placeholder" data-cloudflare-id="24a06ed4ed1b18ff3744d395c3278d34">
                <img src="https://videodelivery.net/24a06ed4ed1b18ff3744d395c3278d34/thumbnails/thumbnail.jpg" class="video-thumbnail" alt="Thumbnail">
                <iframe class="cloudflare-video" src="https://iframe.videodelivery.net/24a06ed4ed1b18ff3744d395c3278d34?autoplay=true&muted=true&loop=true&controls=false" allow="accelerometer; gyroscope; autoplay; encrypted-media; picture-in-picture;" allowfullscreen="true"></iframe>
            </div>
        </div>
    </div>
    <!-- Slide 6 -->
    <div class="content-section">
        <div class="text-box">
            <h2>FPV: La Acción en Primera Persona</h2>
            <p>Drones FPV capaces de seguir la acción con fluidez y precisión, ofreciendo una experiencia inmersiva que conecta al espectador con cada momento.</p>
            <div class="video-placeholder" data-cloudflare-id="ad58d0e92faa4db59a71f9d75a0c3765">
                <img src="https://videodelivery.net/ad58d0e92faa4db59a71f9d75a0c3765/thumbnails/thumbnail.jpg" class="video-thumbnail" alt="Thumbnail">
                <iframe class="cloudflare-video" src="https://iframe.videodelivery.net/ad58d0e92faa4db59a71f9d75a0c3765?autoplay=true&muted=true&loop=true&controls=false" allow="accelerometer; gyroscope; autoplay; encrypted-media; picture-in-picture;" allowfullscreen="true"></iframe>
            </div>
        </div>
    </div>
    <!-- Slide 7 -->
    <div class="content-section">
        <div class="text-box">
            <h2>Drones Cinematicos</h2>
            <p>Planos estabilizados y fluidos, perfectos para cine, televisión y comerciales.</p>
            <div class="video-placeholder" data-cloudflare-id="081a704eafd946e2f4531659b1234d95">
                <img src="https://videodelivery.net/081a704eafd946e2f4531659b1234d95/thumbnails/thumbnail.jpg" class="video-thumbnail" alt="Thumbnail">
                <iframe class="cloudflare-video" src="https://iframe.videodelivery.net/081a704eafd946e2f4531659b1234d95?autoplay=true&muted=true&loop=true&controls=false" allow="accelerometer; gyroscope; autoplay; encrypted-media; picture-in-picture;" allowfullscreen="true"></iframe>
            </div>
        </div>
    </div>
    <!-- Slide 8 -->
    <div class="content-section">
        <div class="text-box">
            <h2>Experiencia en Grandes Producciones</h2>
            <p>Más de seis años cubriendo todo tipo de eventos: conciertos, festivales, producciones de alto perfil y eventos empresariales, siempre con calidad y creatividad.</p>
            <div class="video-placeholder" data-cloudflare-id="1278846ddcf1ee86e90ef589a63e9a96">
                <img src="https://videodelivery.net/1278846ddcf1ee86e90ef589a63e9a96/thumbnails/thumbnail.jpg" class="video-thumbnail" alt="Thumbnail">
                <iframe class="cloudflare-video" src="https://iframe.videodelivery.net/1278846ddcf1ee86e90ef589a63e9a96?autoplay=true&muted=true&loop=true&controls=false" allow="accelerometer; gyroscope; autoplay; encrypted-media; picture-in-picture;" allowfullscreen="true"></iframe>
            </div>
        </div>
    </div>
    <!-- Slide 9 -->
    <div class="content-section">
        <div class="text-box">
            <h2>Seguridad en Cada Vuelo</h2>
            <p>Protocolos estrictos y drones diseñados para interactuar con personas sin riesgos.</p>
            <!-- <div class="video-placeholder" data-cloudflare-id="1278846ddcf1ee86e90ef589a63e9a96">
              <iframe class="cloudflare-video" src="https://iframe.videodelivery.net/1278846ddcf1ee86e90ef589a63e9a96?autoplay=true&muted=true&loop=true&controls=false" allow="accelerometer; gyroscope; autoplay; encrypted-media; picture-in-picture;" allowfullscreen="true"></iframe>
            </div> -->
        </div>
    </div>
    <!-- Slide 10 -->
    <div class="content-section">
        <div class="text-box">
            <h2>Conciertos y Festivales</h2>
            <p>Cobertura aérea de los escenarios más importantes del país, capturando energía y emoción.</p>
            <div class="video-placeholder" data-cloudflare-id="1278846ddcf1ee86e90ef589a63e9a96">
                <img src="https://videodelivery.net/1278846ddcf1ee86e90ef589a63e9a96/thumbnails/thumbnail.jpg" class="video-thumbnail" alt="Thumbnail">
                <iframe class="cloudflare-video" src="https://iframe.videodelivery.net/1278846ddcf1ee86e90ef589a63e9a96?autoplay=true&muted=true&loop=true&controls=false" allow="accelerometer; gyroscope; autoplay; encrypted-media; picture-in-picture;" allowfullscreen="true"></iframe>
            </div>
        </div>
    </div>
    <!-- Slide 11 -->
    <div class="content-section">
        <div class="text-box">
            <h2>Cine y TV</h2>
            <p>Tomas aéreas para comerciales, series y largometrajes, adaptadas a cada historia.</p>
            <!-- <div class="video-placeholder" data-cloudflare-id="1278846ddcf1ee86e90ef589a63e9a96">
              <iframe class="cloudflare-video" src="https://iframe.videodelivery.net/1278846ddcf1ee86e90ef589a63e9a96?autoplay=true&muted=true&loop=true&controls=false" allow="accelerometer; gyroscope; autoplay; encrypted-media; picture-in-picture;" allowfullscreen="true"></iframe>
            </div> -->
        </div>
    </div>
    <!-- Slide 12 -->
    <div class="content-section">
        <div class="text-box">
            <h2>Postproducción Profesional</h2>
            <p>Edición, corrección de color y masterización para transformar las tomas en piezas audiovisuales de alto impacto.</p>
            <div class="video-placeholder" data-cloudflare-id="eb7139eed97ccacbfdd158982ce10890">
                <img src="https://videodelivery.net/eb7139eed97ccacbfdd158982ce10890/thumbnails/thumbnail.jpg" class="video-thumbnail" alt="Thumbnail">
                <iframe class="cloudflare-video" src="https://iframe.videodelivery.net/eb7139eed97ccacbfdd158982ce10890?autoplay=true&muted=true&loop=true&controls=false" allow="accelerometer; gyroscope; autoplay; encrypted-media; picture-in-picture;" allowfullscreen="true"></iframe>
            </div>
        </div>
    </div>
    <!-- Slide 13 -->
    <div class="content-section">
        <div class="text-box">
            <h2>VFX y Composición Digital</h2>
            <p>Efectos visuales y composiciones que amplían las posibilidades creativas de cada proyecto.</p>
            <div class="video-placeholder" data-cloudflare-id="6d341f9c84d4974de838c675027e0a81">
                <img src="https://videodelivery.net/6d341f9c84d4974de838c675027e0a81/thumbnails/thumbnail.jpg" class="video-thumbnail" alt="Thumbnail">
                <iframe class="cloudflare-video" src="https://iframe.videodelivery.net/6d341f9c84d4974de838c675027e0a81?autoplay=true&muted=true&loop=true&controls=false" allow="accelerometer; gyroscope; autoplay; encrypted-media; picture-in-picture;" allowfullscreen="true"></iframe>
            </div>
        </div>
    </div>
    <!-- Slide 14 -->
    <div class="content-section">
        <div class="text-box">
            <h2>Cobertura Nacional</h2>
            <p>Grabamos en cualquier punto de México con todos los permisos y documentación en regla.</p>
            <!-- <div class="video-placeholder" data-cloudflare-id="1278846ddcf1ee86e90ef589a63e9a96">
              <iframe class="cloudflare-video" src="https://iframe.videodelivery.net/1278846ddcf1ee86e90ef589a63e9a96?autoplay=true&muted=true&loop=true&controls=false" allow="accelerometer; gyroscope; autoplay; encrypted-media; picture-in-picture;" allowfullscreen="true"></iframe>
            </div> -->
        </div>
    </div>
    <!-- Slide 15: Final -->
    <div class="content-section">
        <div class="text-box">
            <h2>Producción Audiovisual Integral</h2>
            <p>En Dronexperto México no solo filmamos, creamos experiencias que transforman la forma de ver una historia. Si buscas llevar tu producción a nuevas alturas, contáctanos y hablemos de tu próximo proyecto. Y si quieres inspirarte, explora nuestra biblioteca de videos y descubre lo que podemos lograr juntos desde el aire.</p>

            <!-- <div class="video-placeholder" data-cloudflare-id="cdc1a105e4e5c2b2da0b2a3116b2bd61">
              <iframe class="cloudflare-video" src="https://iframe.videodelivery.net/cdc1a105e4e5c2b2da0b2a3116b2bd61?autoplay=true&muted=true&loop=true&controls=false" allow="accelerometer; gyroscope; autoplay; encrypted-media; picture-in-picture;" allowfullscreen="true"></iframe>
            </div> -->
        </div>
    </div>
</div>

<div id="jukebox-section">
    <h2>Nuestra Filmoteca</h2>
    <p>Explora una selección de nuestros trabajos. Cada video es un pedazo de nuestra pasión.</p>
    <div id="jukebox-grid" class="jukebox-grid"></div>
</div>

<div id="video-modal">
    <span class="close-modal">&times;</span>
    <div class="modal-content"><iframe id="vimeo-player" src="" frameborder="0" allow="autoplay; fullscreen; picture-in-picture" allowfullscreen></iframe></div>
</div>

<script type="importmap">
    {
        "imports": {
            "three": "https://cdn.jsdelivr.net/npm/three@0.128.0/build/three.module.js",
            "three/addons/": "https://cdn.jsdelivr.net/npm/three@0.128.0/examples/jsm/"
        }
    }
</script>
<script type="module">
    import * as THREE from 'three';
    import { CSS3DRenderer, CSS3DObject } from 'three/addons/renderers/CSS3DRenderer.js';

    const videos = [
        { vimeoId: '586826593', title: 'Santa Fe Klan - Cuidando el Territorio', thumbnail: 'https://i.vimeocdn.com/video/1231359679-6b5c65e88e2f3a610444f6f1572c91807f43885d5f265c71a3b198a08f5d94f9-d_640' },
        { vimeoId: '708035254', title: 'Gera MX - Prohibida', thumbnail: 'https://i.vimeocdn.com/video/1429285994-55a7995180630b209d66649f8072efd0295842828551480f72365315848c78c3-d_640' },
        { vimeoId: '791997463', title: 'Mexico Mágico - Fitur 2023', thumbnail: 'https://i.vimeocdn.com/video/1597813281-295b9a101f6535492d3f746522f483a992523f3609385392e272a275f137a89e-d_640' },
    ];

    let camera, webglScene, cssScene, webglRenderer, cssRenderer, stars, pointLight, pyramidGroup;
    let raceTrackCurve;
    const waypoints = [];
    const slideProgress = Array.from({length: 15}, (_, i) => i / 15);

    function init() {
        camera = new THREE.PerspectiveCamera(75, window.innerWidth / window.innerHeight, 10, 40000);

        webglScene = new THREE.Scene();
        const ambientLight = new THREE.AmbientLight(0xffffff, 0.2);
        webglScene.add(ambientLight);


        pointLight = new THREE.PointLight(0xffffff, 0.4, 0);
        webglScene.add(pointLight);

        webglRenderer = new THREE.WebGLRenderer({
            canvas: document.getElementById('three-canvas'),
            antialias: true,
            alpha: true
        });
        webglRenderer.setSize(window.innerWidth, window.innerHeight);
        webglRenderer.setPixelRatio(window.devicePixelRatio);

        cssScene = new THREE.Scene();
        cssRenderer = new CSS3DRenderer();
        cssRenderer.setSize(window.innerWidth, window.innerHeight);
        document.getElementById('css-renderer').appendChild(cssRenderer.domElement);

        pyramidGroup = new THREE.Group();
        webglScene.add(pyramidGroup);

        createRaceTrack();
        createStars();
        createFloatingObjects();
        createContentSlides();
        initializeVideoThumbnails();

        animate();
        window.addEventListener('scroll', handleScroll);
        window.addEventListener('resize', onWindowResize);
        window.addEventListener('keydown', handleKeyNavigation);

        setTimeout(handleScroll, 100);
    }

    function createRaceTrack() {
        const points = [
            new THREE.Vector3(0, 0, 0),
            new THREE.Vector3(0, 0, -2500),
            new THREE.Vector3(1000, 100, -5000),
            new THREE.Vector3(1500, -250, -7500),
            new THREE.Vector3(1000, 300, -10000),
            new THREE.Vector3(0, -300, -12500),
            new THREE.Vector3(-700, 100, -15000),
            new THREE.Vector3(-1200, 200, -17500),
            new THREE.Vector3(-700, -200, -20000),
            new THREE.Vector3(0, 0, -22500),
            new THREE.Vector3(0, 0, -25000),
            new THREE.Vector3(400, -100, -27500),
            new THREE.Vector3(-400, 100, -30000),
            new THREE.Vector3(0, 0, -32500),
            new THREE.Vector3(0, 0, -35000)
        ];
        raceTrackCurve = new THREE.CatmullRomCurve3(points);

        const lineGeometry = new THREE.BufferGeometry().setFromPoints(raceTrackCurve.getPoints(300));
        const lineMaterial = new THREE.LineBasicMaterial({ color: 0xdbfe0d, transparent: true, opacity: 0.2 });
        webglScene.add(new THREE.Line(lineGeometry, lineMaterial));
    }

    function createStars() {
        const starVertices = [];
        for (let i = 0; i < 60000; i++) {
            const x = (Math.random() - 0.5) * 35000;
            const y = (Math.random() - 0.5) * 35000;
            const z = (Math.random() - 0.5) * 35000;
            starVertices.push(x, y, z);
        }
        const starGeometry = new THREE.BufferGeometry();
        starGeometry.setAttribute('position', new THREE.Float32BufferAttribute(starVertices, 3));
        const starMaterial = new THREE.PointsMaterial({ color: 0xffffff, size: 2.5 });
        stars = new THREE.Points(starGeometry, starMaterial);
        webglScene.add(stars);
    }

    function createFloatingObjects() {
        const pyramidGeometry = new THREE.TetrahedronGeometry(1); // Base size
        const greenMaterial = new THREE.MeshStandardMaterial({ color: 0xdbfe0d, roughness: 0.5 });

        for (let i = 0; i < 50; i++) {
            const point = raceTrackCurve.getPointAt(Math.random());
            const offsetDirection = new THREE.Vector3(Math.random() - 0.5, Math.random() - 0.5, Math.random() - 0.5).normalize();
            const distance = 3500 + Math.random() * 200;
            const offset = offsetDirection.multiplyScalar(distance);
            const objectPosition = point.add(offset);

            const object = new THREE.Mesh(pyramidGeometry, greenMaterial);

            const scale = (Math.random() * 900) + 50; // Random scale between 50 and 150
            object.scale.set(scale, scale, scale);

            object.position.copy(objectPosition);
            object.rotation.set(Math.random() * Math.PI, Math.random() * Math.PI, Math.random() * Math.PI);

            pyramidGroup.add(object);
        }
    }

    function createContentSlides() {
        const sourceElements = document.querySelectorAll('#content-source .content-section');
        sourceElements.forEach((el, i) => {
            const positionOnCurve = raceTrackCurve.getPointAt(slideProgress[i]);
            const cssObject = new CSS3DObject(el);
            cssObject.position.copy(positionOnCurve);
            cssScene.add(cssObject);
        });
        calculateWaypoints();
    }

    function calculateWaypoints() {
        const scrollContainerHeight = document.querySelector('.scroll-container').scrollHeight;
        const windowHeight = window.innerHeight;
        const totalScrollable = scrollContainerHeight - windowHeight;

        waypoints.length = 0;
        slideProgress.forEach(progress => {
            waypoints.push(progress * totalScrollable);
        });
    }

    function animate() {
        requestAnimationFrame(animate);

        // MODIFICACIÓN: Rotación automática y constante basada en el tiempo
        const time = Date.now() * 0.0001; // Puedes ajustar la velocidad cambiando este valor
        if (pyramidGroup) {
            pyramidGroup.rotation.z = time; // Rotación constante en el eje Z
        }

        webglRenderer.render(webglScene, camera);
        cssRenderer.render(cssScene, camera);
    }

    function handleScroll() {
        const scrollPosition = window.scrollY;
        const scrollContainerHeight = document.querySelector('.scroll-container').scrollHeight;
        const windowHeight = window.innerHeight;
        const totalScrollable = scrollContainerHeight - windowHeight;

        if (totalScrollable <= 0) return;

        let scrollFraction = scrollPosition / totalScrollable;
        scrollFraction = Math.min(Math.max(scrollFraction, 0), 1);

        const contentPoint = raceTrackCurve.getPointAt(scrollFraction);
        const lookAtPoint = raceTrackCurve.getPointAt(Math.min(scrollFraction + 0.001, 1));

        const direction = new THREE.Vector3().subVectors(contentPoint, lookAtPoint).normalize();
        const cameraPosition = contentPoint.clone().addScaledVector(direction, 800);

        camera.position.copy(cameraPosition);
        pointLight.position.copy(cameraPosition);
        camera.lookAt(contentPoint);

        cssScene.children.forEach(object => {
            object.rotation.copy(camera.rotation);
        });

        const introCard = document.querySelector('#intro-card .text-box');
        if (introCard) {
            const fadeEnd = 300;
            const opacity = 1 - Math.min(scrollPosition / fadeEnd, 1);
            introCard.style.backgroundColor = `rgba(0, 0, 0, ${opacity})`;
        }
    }

    function handleKeyNavigation(e) {
        if (e.key !== 'ArrowRight' && e.key !== 'ArrowLeft') return;

        const currentScroll = window.scrollY;
        let targetIndex;

        if (e.key === 'ArrowRight') {
            targetIndex = waypoints.findIndex(w => w > currentScroll + 1);
            if (targetIndex === -1) targetIndex = waypoints.length - 1;
        } else { // ArrowLeft
            const prevWaypoints = waypoints.filter(w => w < currentScroll - 1);
            targetIndex = prevWaypoints.length > 0 ? waypoints.indexOf(prevWaypoints[prevWaypoints.length - 1]) : 0;
        }

        window.scrollTo({
            top: waypoints[targetIndex],
            behavior: 'smooth'
        });
    }

    function onWindowResize() {
        camera.aspect = window.innerWidth / window.innerHeight;
        camera.updateProjectionMatrix();
        webglRenderer.setSize(window.innerWidth, window.innerHeight);
        cssRenderer.setSize(window.innerWidth, window.innerHeight);
        calculateWaypoints();
    }

    function initializeVideoThumbnails() {
        const placeholders = document.querySelectorAll('.video-placeholder');
        placeholders.forEach(placeholder => {
            const iframe = placeholder.querySelector('iframe');
            if (iframe) {
                iframe.addEventListener('load', () => {
                    placeholder.classList.add('loaded');
                });
            }
        });
    }

    const jukeboxGrid = document.querySelector('#jukebox-section .jukebox-grid');
    const videoModal = document.getElementById('video-modal');
    const vimeoPlayer = document.getElementById('vimeo-player');
    const closeModalBtn = document.querySelector('.close-modal');

    function populateJukebox() {
        if (!jukeboxGrid) return;
        jukeboxGrid.innerHTML = '';
        videos.forEach(video => {
            const card = document.createElement('div');
            card.classList.add('video-card');
            card.innerHTML = `<img src="${video.thumbnail}" alt="Miniatura de ${video.title}"><div class="video-card-title">${video.title}</div>`;
            card.addEventListener('click', () => openModal(video.vimeoId));
            jukeboxGrid.appendChild(card);
        });
    }

    function openModal(vimeoId) {
        vimeoPlayer.src = `https://player.vimeo.com/video/${vimeoId}?autoplay=1&color=dbfe0d&title=0&byline=0&portrait=0`;
        videoModal.style.display = 'flex';
    }

    function closeModal() {
        vimeoPlayer.src = '';
        videoModal.style.display = 'none';
    }

    closeModalBtn.addEventListener('click', closeModal);
    videoModal.addEventListener('click', (e) => { if (e.target === videoModal) closeModal(); });

    init();
    populateJukebox();
    initializeVideoThumbnails();
</script>
</body>
</html>
