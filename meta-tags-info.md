# Meta Tags para WhatsApp - Dronexperto

## 📱 Meta Tags Implementados

### Open Graph (WhatsApp, Facebook, LinkedIn)
```html
<meta property="og:title" content="Dronexperto - Cinematografía Aérea Profesional">
<meta property="og:description" content="Llevamos tus ideas a nuevas alturas. Filmación aérea profesional con drones, tecnología propia y más de 6 años de experiencia en México.">
<meta property="og:image" content="https://dronexperto.com/filmacion-con-drones/images/dronexperto_og_image.jpg">
<meta property="og:url" content="https://dronexperto.com/filmacion-con-drones/">
<meta property="og:type" content="website">
```

### Twitter Cards
```html
<meta name="twitter:card" content="summary_large_image">
<meta name="twitter:title" content="Dronexperto - Cinematografía Aérea Profesional">
<meta name="twitter:description" content="Filmación aérea profesional con drones en México. Tecnología propia, pilotos certificados y experiencias visuales únicas.">
<meta name="twitter:image" content="https://dronexperto.com/filmacion-con-drones/images/dronexperto_og_image.jpg">
```

## 🖼️ Especificaciones de la Imagen Open Graph

### Requisitos técnicos:
- **Tamaño recomendado**: 1200x630 píxeles
- **Formato**: JPG o PNG
- **Peso máximo**: 8MB (recomendado < 1MB)
- **Relación de aspecto**: 1.91:1

### Contenido sugerido para la imagen:
1. **Logo de Dronexperto** prominente
2. **Texto**: "Cinematografía Aérea Profesional"
3. **Imagen de fondo**: Drone en acción o toma aérea espectacular
4. **Colores**: Negro, verde neón (#dbfe0d), morado (#8A2BE2)
5. **Texto adicional**: "México" o "Tecnología Propia"

## 📋 Checklist para implementar:

### ✅ Ya implementado:
- [x] Meta tags Open Graph
- [x] Twitter Cards
- [x] Meta description
- [x] Meta keywords
- [x] Favicon references

### 🔄 Pendiente:
- [ ] Crear imagen Open Graph (1200x630px)
- [ ] Subir imagen al servidor
- [ ] Actualizar URL de la imagen en los meta tags
- [ ] Crear favicon.ico
- [ ] Crear apple-touch-icon.png
- [ ] Probar en WhatsApp Web

## 🧪 Cómo probar:

1. **WhatsApp Web**: Pegar la URL y ver la preview
2. **Facebook Debugger**: https://developers.facebook.com/tools/debug/
3. **Twitter Card Validator**: https://cards-dev.twitter.com/validator
4. **LinkedIn Post Inspector**: https://www.linkedin.com/post-inspector/

## 📱 Resultado esperado en WhatsApp:

Cuando alguien comparta tu URL en WhatsApp, aparecerá:
- **Imagen**: Tu imagen Open Graph (1200x630)
- **Título**: "Dronexperto - Cinematografía Aérea Profesional"
- **Descripción**: "Llevamos tus ideas a nuevas alturas..."
- **URL**: dronexperto.com

## 🎨 Sugerencias para la imagen:

### Opción 1 - Minimalista:
- Fondo negro con efecto de estrellas
- Logo Dronexperto centrado
- Texto "Cinematografía Aérea Profesional"
- Acento en verde neón

### Opción 2 - Acción:
- Imagen de drone FPV en vuelo
- Logo en esquina superior
- Overlay con texto
- Efectos de movimiento

### Opción 3 - Portafolio:
- Collage de tomas aéreas
- Logo prominente
- Texto descriptivo
- Estilo cinematográfico
