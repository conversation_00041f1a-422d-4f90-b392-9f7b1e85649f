<!DOCTYPE html>
<html lang="es">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Dronexperto - Cinematografía Aérea Profesional</title>
  <style>
    :root {
      --neon-green: #dbfe0d;
      --purple: #8A2BE2;
      --white: #FFFFFF;
      --black: #000000;
    }

    body {
      background-color: var(--black);
      color: var(--white);
      margin: 0;
      padding: 0;
      font-family: 'Inter', 'Helvetica Neue', Helvetica, Arial, sans-serif;
      overflow-x: hidden;
    }

    /* Contenedores para los renderers de Three.js */
    .three-container {
      position: fixed;
      top: 0;
      left: 0;
      width: 100%;
      height: 100vh;
      outline: none;
      pointer-events: none;
    }

    #three-canvas {
      z-index: 1;
    }

    #css-renderer {
      pointer-events: auto;
      z-index: 3;
    }

    /* Contenedor que genera la barra de scroll */
    .scroll-container {
      position: relative;
      width: 100%;
      height: 4500vh;
      z-index: 1;
    }

    #content-source {
      display: none;
    }

    .content-section {
      width: 800px;
      pointer-events: auto;
    }

    /* Efecto de flotación puro */
    .text-box {
      max-width: 600px;
      margin: 0 auto;
      padding: 1rem;
      text-align: center;
      background: transparent;
      border: none;
      transition: background-color 0.5s ease;
    }

    /* Card de introducción con fondo negro */
    #intro-card .text-box {
      background-color: var(--black);
    }

    /* Títulos con efecto 3D nítido y brillo sutil */
    .text-box h1, .text-box h2 {
      font-size: 2.8rem;
      font-weight: bold;
      color: var(--neon-green);
      margin-bottom: 1.5rem;
      text-shadow:
              0 0 10px rgba(219, 254, 13, 0.7),
              0 1px 0 #849907,
              0 2px 0 #849907,
              0 3px 0 #849907;
    }

    .text-box p {
      font-size: 1.2rem;
      line-height: 1.7;
      text-shadow: 0 0 8px rgba(0,0,0,0.8);
    }

    .highlight {
      color: var(--neon-green);
      font-weight: bold;
    }

    #logo-container {
      max-width: 250px;
      margin: 0 auto 1rem;
    }

    #logo-container img {
      width: 100%;
      height: auto;
      filter: drop-shadow(0 0 15px var(--neon-green));
    }

    .video-placeholder {
      width: 100%;
      padding-top: 56.25%;
      position: relative;
      background: transparent;
      border-radius: 0.5rem;
      margin-top: 1.5rem;
      border: none;
      display: flex;
      align-items: center;
      justify-content: center;
      overflow: hidden;
    }

    .video-placeholder iframe {
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      border-radius: 1rem;
      box-shadow: 0 0 25px rgba(219, 254, 13, 0.4);
      opacity: 0;
      transition: opacity 0.5s ease;
    }

    .video-placeholder iframe.loaded {
      opacity: 1;
    }

    .video-thumbnail {
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background-size: cover;
      background-position: center;
      border-radius: 1rem;
      box-shadow: 0 0 25px rgba(219, 254, 13, 0.4);
      display: flex;
      align-items: center;
      justify-content: center;
      transition: opacity 0.5s ease;
      cursor: pointer;
    }

    .video-thumbnail.hidden {
      opacity: 0;
      pointer-events: none;
    }

    .play-button {
      width: 80px;
      height: 80px;
      background: rgba(219, 254, 13, 0.9);
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      transition: all 0.3s ease;
      cursor: pointer;
    }

    .play-button:hover {
      background: rgba(219, 254, 13, 1);
      transform: scale(1.1);
    }

    .play-button::after {
      content: '';
      width: 0;
      height: 0;
      border-left: 20px solid #000;
      border-top: 12px solid transparent;
      border-bottom: 12px solid transparent;
      margin-left: 4px;
    }

    .loading-spinner {
      position: absolute;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
      width: 40px;
      height: 40px;
      border: 3px solid rgba(219, 254, 13, 0.3);
      border-top: 3px solid var(--neon-green);
      border-radius: 50%;
      animation: spin 1s linear infinite;
      display: none;
    }

    @keyframes spin {
      0% { transform: translate(-50%, -50%) rotate(0deg); }
      100% { transform: translate(-50%, -50%) rotate(360deg); }
    }

    #jukebox-section {
      position: relative;
      padding: 10vh 5%;
      text-align: center;
      background-color: var(--black);
      z-index: 2;
    }

    .jukebox-grid {
      display: grid;
      grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
      gap: 1.5rem;
      margin-top: 2rem;
    }

    .video-card {
      background: #111;
      border-radius: 1rem;
      overflow: hidden;
      cursor: pointer;
      transition: transform 0.3s ease, box-shadow 0.3s ease;
      border: 1px solid #333;
    }

    .video-card:hover {
      transform: scale(1.05);
      box-shadow: 0 0 20px var(--neon-green);
    }

    .video-card img {
      width: 100%;
      height: 180px;
      object-fit: cover;
      display: block;
    }

    .video-card-title {
      padding: 1rem;
      font-size: 1rem;
      font-weight: bold;
      color: var(--white);
    }

    #video-modal {
      position: fixed;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background: rgba(0, 0, 0, 0.9);
      display: none;
      justify-content: center;
      align-items: center;
      z-index: 1000;
    }

    .modal-content {
      position: relative;
      width: 90%;
      max-width: 960px;
      padding-top: 56.25%;
    }

    .modal-content iframe {
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      border: none;
    }

    .close-modal {
      position: absolute;
      top: 20px;
      right: 30px;
      font-size: 3rem;
      color: var(--white);
      cursor: pointer;
      transition: color 0.3s ease;
    }

    .close-modal:hover {
      color: var(--neon-green);
    }

    @media (max-width: 768px) {
      .text-box h1, .text-box h2 { font-size: 2rem; }
      .text-box p { font-size: 1rem; }
      .content-section { width: 90vw; }
    }
  </style>
</head>
<body>
<canvas id="three-canvas" class="three-container"></canvas>
<div id="css-renderer" class="three-container"></div>

<div class="scroll-container"></div>

<div id="content-source">
  <!-- Slide 1: Intro con ID para el card -->
  <div id="intro-card" class="content-section">
    <div class="text-box">
      <div id="logo-container">
        <img src="https://dronexperto.com/filmacion-con-drones/images/dronexperto_logo_neon.svg" alt="Logo Dronexperto Neon">
      </div>
      <h1>Filmación Aérea Profesional con Drones</h1>
      <p>En Dronexperto México creemos que cada historia merece ser contada desde la mejor perspectiva. Nuestros drones no solo vuelan, crean emociones. Diseñamos y fabricamos cada uno a la medida de tu proyecto, pilotados con precisión y respaldados por años de experiencia para lograr tomas únicas y seguras. Ya sea en la intensidad de un concierto, la fuerza de un evento corporativo o la magia de una producción cinematográfica, llevamos tus ideas a nuevas alturas. Con postproducción y VFX, convertimos cada imagen en una experiencia visual que impacta y conecta con tu audiencia.</p>
    </div>
  </div>
  <!-- Slide 2 -->
  <div class="content-section">
    <div class="text-box">
      <h2>Una Nueva Perspectiva</h2>
      <p>Vemos el mundo desde ángulos que pocos pueden alcanzar. Creamos imágenes aéreas que convierten cualquier proyecto en una experiencia inmersiva.</p>
      <div class="video-placeholder" data-vimeo-id="1108779800">
        <div class="video-thumbnail" style="background-image: url('https://i.vimeocdn.com/video/1108779800-640x360.jpg')">
          <div class="play-button"></div>
        </div>
        <div class="loading-spinner"></div>
        <iframe data-src="https://player.vimeo.com/video/1108779800?background=1&autoplay=1&loop=1&byline=0&title=0&muted=1" frameborder="0" allow="autoplay; fullscreen" allowfullscreen></iframe>
      </div>
    </div>
  </div>
  <!-- Slide 3 -->
  <div class="content-section">
    <div class="text-box">
      <h2>Contamos Historias desde el Aire</h2>
      <p>No es solo grabar: es narrar tu historia con emoción y creatividad, desde un punto de vista único.</p>
      <div class="video-placeholder" data-vimeo-id="807760363">
        <div class="video-thumbnail" style="background-image: url('https://i.vimeocdn.com/video/1429285994-640x360.jpg')">
          <div class="play-button"></div>
        </div>
        <div class="loading-spinner"></div>
        <iframe data-src="https://player.vimeo.com/video/807760363?background=1&autoplay=1&loop=1&byline=0&title=0&muted=1" frameborder="0" allow="autoplay; fullscreen" allowfullscreen></iframe>
      </div>
    </div>
  </div>
  <!-- Slide 4 -->
  <div class="content-section">
    <div class="text-box">
      <h2>Tecnología Propia</h2>
      <p>Diseñamos y fabricamos nuestros drones para lograr tomas imposibles con la máxima seguridad.</p>
      <div class="video-placeholder" data-vimeo-id="807756796">
        <div class="video-thumbnail" style="background-image: url('https://i.vimeocdn.com/video/1597813281-640x360.jpg')">
          <div class="play-button"></div>
        </div>
        <div class="loading-spinner"></div>
        <iframe data-src="https://player.vimeo.com/video/807756796?background=1&autoplay=1&loop=1&byline=0&title=0&muted=1" frameborder="0" allow="autoplay; fullscreen" allowfullscreen></iframe>
      </div>
    </div>
  </div>
  <!-- Slide 5 -->
  <div class="content-section">
    <div class="text-box">
      <h2>Precisión Profesional</h2>
      <p>Pilotos altamente capacitados que vuelan con exactitud milimétrica en entornos complejos y espacios reducidos.</p>
      <div class="video-placeholder" data-vimeo-id="807756273">
        <div class="video-thumbnail" style="background-image: url('https://i.vimeocdn.com/video/1597813281-640x360.jpg')">
          <div class="play-button"></div>
        </div>
        <div class="loading-spinner"></div>
        <iframe data-src="https://player.vimeo.com/video/807756273?background=1&autoplay=1&loop=1&byline=0&title=0&muted=1" frameborder="0" allow="autoplay; fullscreen" allowfullscreen></iframe>
      </div>
    </div>
  </div>
  <!-- Slide 6 -->
  <div class="content-section">
    <div class="text-box">
      <h2>FPV: La Acción en Primera Persona</h2>
      <p>Drones FPV capaces de seguir la acción con fluidez y precisión, ofreciendo una experiencia inmersiva que conecta al espectador con cada momento.</p>
      <div class="video-placeholder" data-vimeo-id="788198472">
        <div class="video-thumbnail" style="background-image: url('https://i.vimeocdn.com/video/1597813281-640x360.jpg')">
          <div class="play-button"></div>
        </div>
        <div class="loading-spinner"></div>
        <iframe data-src="https://player.vimeo.com/video/788198472?background=1&autoplay=1&loop=1&byline=0&title=0&muted=1" frameborder="0" allow="autoplay; fullscreen" allowfullscreen></iframe>
      </div>
    </div>
  </div>
  <!-- Slide 7 -->
  <div class="content-section">
    <div class="text-box">
      <h2>Drones Cinematicos</h2>
      <p>Planos estabilizados y fluidos, perfectos para cine, televisión y comerciales.</p>
      <div class="video-placeholder" data-vimeo-id="807756273">
        <div class="video-thumbnail" style="background-image: url('https://i.vimeocdn.com/video/1597813281-640x360.jpg')">
          <div class="play-button"></div>
        </div>
        <div class="loading-spinner"></div>
        <iframe data-src="https://player.vimeo.com/video/807756273?background=1&autoplay=1&loop=1&byline=0&title=0&muted=1" frameborder="0" allow="autoplay; fullscreen" allowfullscreen></iframe>
      </div>
    </div>
  </div>
  <!-- Slide 8 -->
  <div class="content-section">
    <div class="text-box">
      <h2>Experiencia en Grandes Producciones</h2>
      <p>Más de seis años cubriendo todo tipo de eventos: conciertos, festivales, producciones de alto perfil y eventos empresariales, siempre con calidad y creatividad.</p>
      <div class="video-placeholder" data-vimeo-id="807756273">
        <div class="video-thumbnail" style="background-image: url('https://i.vimeocdn.com/video/1597813281-640x360.jpg')">
          <div class="play-button"></div>
        </div>
        <div class="loading-spinner"></div>
        <iframe data-src="https://player.vimeo.com/video/807756273?background=1&autoplay=1&loop=1&byline=0&title=0&muted=1" frameborder="0" allow="autoplay; fullscreen" allowfullscreen></iframe>
      </div>
    </div>
  </div>
  <!-- Slide 9 -->
  <div class="content-section">
    <div class="text-box">
      <h2>Seguridad en Cada Vuelo</h2>
      <p>Protocolos estrictos y drones diseñados para interactuar con personas sin riesgos.</p>
      <div class="video-placeholder" data-vimeo-id="790607149">
        <div class="video-thumbnail" style="background-image: url('https://i.vimeocdn.com/video/1597813281-640x360.jpg')">
          <div class="play-button"></div>
        </div>
        <div class="loading-spinner"></div>
        <iframe data-src="https://player.vimeo.com/video/790607149?background=1&autoplay=1&loop=1&byline=0&title=0&muted=1" frameborder="0" allow="autoplay; fullscreen" allowfullscreen></iframe>
      </div>
    </div>
  </div>
  <!-- Slide 10 -->
  <div class="content-section">
    <div class="text-box">
      <h2>Conciertos y Festivales</h2>
      <p>Cobertura aérea de los escenarios más importantes del país, capturando energía y emoción.</p>
      <div class="video-placeholder" data-vimeo-id="790607149">
        <div class="video-thumbnail" style="background-image: url('https://i.vimeocdn.com/video/1597813281-640x360.jpg')">
          <div class="play-button"></div>
        </div>
        <div class="loading-spinner"></div>
        <iframe data-src="https://player.vimeo.com/video/790607149?background=1&autoplay=1&loop=1&byline=0&title=0&muted=1" frameborder="0" allow="autoplay; fullscreen" allowfullscreen></iframe>
      </div>
    </div>
  </div>
  <!-- Slide 11 -->
  <div class="content-section">
    <div class="text-box">
      <h2>Cine y TV</h2>
      <p>Tomas aéreas para comerciales, series y largometrajes, adaptadas a cada historia.</p>
      <div class="video-placeholder" data-vimeo-id="790607149">
        <div class="video-thumbnail" style="background-image: url('https://i.vimeocdn.com/video/1597813281-640x360.jpg')">
          <div class="play-button"></div>
        </div>
        <div class="loading-spinner"></div>
        <iframe data-src="https://player.vimeo.com/video/790607149?background=1&autoplay=1&loop=1&byline=0&title=0&muted=1" frameborder="0" allow="autoplay; fullscreen" allowfullscreen></iframe>
      </div>
    </div>
  </div>
  <!-- Slide 12 -->
  <div class="content-section">
    <div class="text-box">
      <h2>Postproducción Profesional</h2>
      <p>Edición, corrección de color y masterización para transformar las tomas en piezas audiovisuales de alto impacto.</p>
      <div class="video-placeholder" data-vimeo-id="790607149">
        <div class="video-thumbnail" style="background-image: url('https://i.vimeocdn.com/video/1597813281-640x360.jpg')">
          <div class="play-button"></div>
        </div>
        <div class="loading-spinner"></div>
        <iframe data-src="https://player.vimeo.com/video/790607149?background=1&autoplay=1&loop=1&byline=0&title=0&muted=1" frameborder="0" allow="autoplay; fullscreen" allowfullscreen></iframe>
      </div>
    </div>
  </div>
  <!-- Slide 13 -->
  <div class="content-section">
    <div class="text-box">
      <h2>VFX y Composición Digital</h2>
      <p>Efectos visuales y composiciones que amplían las posibilidades creativas de cada proyecto.</p>
      <div class="video-placeholder" data-vimeo-id="790607149">
        <div class="video-thumbnail" style="background-image: url('https://i.vimeocdn.com/video/1597813281-640x360.jpg')">
          <div class="play-button"></div>
        </div>
        <div class="loading-spinner"></div>
        <iframe data-src="https://player.vimeo.com/video/790607149?background=1&autoplay=1&loop=1&byline=0&title=0&muted=1" frameborder="0" allow="autoplay; fullscreen" allowfullscreen></iframe>
      </div>
    </div>
  </div>
  <!-- Slide 14 -->
  <div class="content-section">
    <div class="text-box">
      <h2>Cobertura Nacional</h2>
      <p>Grabamos en cualquier punto de México con todos los permisos y documentación en regla.</p>
      <div class="video-placeholder" data-vimeo-id="790607149">
        <div class="video-thumbnail" style="background-image: url('https://i.vimeocdn.com/video/1597813281-640x360.jpg')">
          <div class="play-button"></div>
        </div>
        <div class="loading-spinner"></div>
        <iframe data-src="https://player.vimeo.com/video/790607149?background=1&autoplay=1&loop=1&byline=0&title=0&muted=1" frameborder="0" allow="autoplay; fullscreen" allowfullscreen></iframe>
      </div>
    </div>
  </div>
  <!-- Slide 15: Final -->
  <div class="content-section">
    <div class="text-box">
      <h2>Producción Audiovisual Integral</h2>
      <p>En Dronexperto México no solo filmamos, creamos experiencias que transforman la forma de ver una historia. Si buscas llevar tu producción a nuevas alturas, contáctanos y hablemos de tu próximo proyecto. Y si quieres inspirarte, explora nuestra biblioteca de videos y descubre lo que podemos lograr juntos desde el aire.</p>

      <div class="video-placeholder" data-vimeo-id="790607149">
        <div class="video-thumbnail" style="background-image: url('https://i.vimeocdn.com/video/1597813281-640x360.jpg')">
          <div class="play-button"></div>
        </div>
        <div class="loading-spinner"></div>
        <iframe data-src="https://player.vimeo.com/video/790607149?background=1&autoplay=1&loop=1&byline=0&title=0&muted=1" frameborder="0" allow="autoplay; fullscreen" allowfullscreen></iframe>
      </div>
    </div>
  </div>
</div>

<div id="jukebox-section">
  <h2>Nuestra Filmoteca</h2>
  <p>Explora una selección de nuestros trabajos. Cada video es un pedazo de nuestra pasión.</p>
  <div id="jukebox-grid" class="jukebox-grid"></div>
</div>

<div id="video-modal">
  <span class="close-modal">&times;</span>
  <div class="modal-content"><iframe id="vimeo-player" src="" frameborder="0" allow="autoplay; fullscreen; picture-in-picture" allowfullscreen></iframe></div>
</div>

<script type="importmap">
  {
      "imports": {
          "three": "https://cdn.jsdelivr.net/npm/three@0.128.0/build/three.module.js",
          "three/addons/": "https://cdn.jsdelivr.net/npm/three@0.128.0/examples/jsm/"
      }
  }
</script>
<script type="module">
  import * as THREE from 'three';
  import { CSS3DRenderer, CSS3DObject } from 'three/addons/renderers/CSS3DRenderer.js';

  const videos = [
    { vimeoId: '586826593', title: 'Santa Fe Klan - Cuidando el Territorio', thumbnail: 'https://i.vimeocdn.com/video/1231359679-6b5c65e88e2f3a610444f6f1572c91807f43885d5f265c71a3b198a08f5d94f9-d_640' },
    { vimeoId: '708035254', title: 'Gera MX - Prohibida', thumbnail: 'https://i.vimeocdn.com/video/1429285994-55a7995180630b209d66649f8072efd0295842828551480f72365315848c78c3-d_640' },
    { vimeoId: '791997463', title: 'Mexico Mágico - Fitur 2023', thumbnail: 'https://i.vimeocdn.com/video/1597813281-295b9a101f6535492d3f746522f483a992523f3609385392e272a275f137a89e-d_640' },
  ];

  let camera, webglScene, cssScene, webglRenderer, cssRenderer, stars, pointLight;
  let raceTrackCurve;
  const waypoints = [];
  const slideProgress = Array.from({length: 15}, (_, i) => i / 14);

  function init() {
    camera = new THREE.PerspectiveCamera(75, window.innerWidth / window.innerHeight, 10, 40000);

    webglScene = new THREE.Scene();
    const ambientLight = new THREE.AmbientLight(0xffffff, 0.6);
    webglScene.add(ambientLight);

    pointLight = new THREE.PointLight(0xffffff, 0.8, 0);
    webglScene.add(pointLight);

    webglRenderer = new THREE.WebGLRenderer({
      canvas: document.getElementById('three-canvas'),
      antialias: true,
      alpha: true
    });
    webglRenderer.setSize(window.innerWidth, window.innerHeight);
    webglRenderer.setPixelRatio(window.devicePixelRatio);

    cssScene = new THREE.Scene();
    cssRenderer = new CSS3DRenderer();
    cssRenderer.setSize(window.innerWidth, window.innerHeight);
    document.getElementById('css-renderer').appendChild(cssRenderer.domElement);

    createRaceTrack();
    createStars();
    createFloatingObjects();
    createContentSlides();
    setupThumbnailClicks();
    loadRealThumbnails();

    animate();
    window.addEventListener('scroll', handleScroll);
    window.addEventListener('resize', onWindowResize);
    window.addEventListener('keydown', handleKeyNavigation);

    // ** CORRECCIÓN: Llamar a handleScroll después de un breve retraso para asegurar que todo esté calculado **
    setTimeout(handleScroll, 100);
  }

  function createRaceTrack() {
    const points = [
      new THREE.Vector3(0, 0, 0),
      new THREE.Vector3(0, 0, -2500),
      new THREE.Vector3(1000, 100, -5000),
      new THREE.Vector3(1500, -250, -7500),
      new THREE.Vector3(1000, 300, -10000),
      new THREE.Vector3(0, -300, -12500),
      new THREE.Vector3(-700, 100, -15000),
      new THREE.Vector3(-1200, 200, -17500),
      new THREE.Vector3(-700, -200, -20000),
      new THREE.Vector3(0, 0, -22500),
      new THREE.Vector3(0, 0, -25000),
      new THREE.Vector3(400, -100, -27500),
      new THREE.Vector3(-400, 100, -30000),
      new THREE.Vector3(0, 0, -32500),
      new THREE.Vector3(0, 0, -35000)
    ];
    raceTrackCurve = new THREE.CatmullRomCurve3(points);

    const lineGeometry = new THREE.BufferGeometry().setFromPoints(raceTrackCurve.getPoints(300));
    const lineMaterial = new THREE.LineBasicMaterial({ color: 0xdbfe0d, transparent: true, opacity: 0.2 });
    webglScene.add(new THREE.Line(lineGeometry, lineMaterial));
  }

  function createStars() {
    const starVertices = [];
    for (let i = 0; i < 60000; i++) {
      const x = (Math.random() - 0.5) * 35000;
      const y = (Math.random() - 0.5) * 35000;
      const z = (Math.random() - 0.5) * 35000;
      starVertices.push(x, y, z);
    }
    const starGeometry = new THREE.BufferGeometry();
    starGeometry.setAttribute('position', new THREE.Float32BufferAttribute(starVertices, 3));
    const starMaterial = new THREE.PointsMaterial({ color: 0xffffff, size: 2.5 });
    stars = new THREE.Points(starGeometry, starMaterial);
    webglScene.add(stars);
  }

  function createFloatingObjects() {
    const ringGeometry = new THREE.TorusGeometry(35, 4, 8, 20);
    const sphereGeometry = new THREE.SphereGeometry(15, 16, 16);

    const greenMaterial = new THREE.MeshStandardMaterial({ color: 0xdbfe0d, roughness: 0.5 });
    const purpleMaterial = new THREE.MeshStandardMaterial({ color: 0x8A2BE2, roughness: 0.5 });

    for (let i = 0; i < 500; i++) {
      const point = raceTrackCurve.getPointAt(Math.random());
      const offsetDirection = new THREE.Vector3(Math.random() - 0.5, Math.random() - 0.5, Math.random() - 0.5).normalize();
      const distance = 800 + Math.random() * 1200;
      const offset = offsetDirection.multiplyScalar(distance);
      const objectPosition = point.add(offset);

      let object;
      if (Math.random() > 0.1) object = new THREE.Mesh(sphereGeometry, purpleMaterial);
      else object = new THREE.Mesh(sphereGeometry, greenMaterial);

      object.position.copy(objectPosition);
      object.rotation.set(Math.random() * Math.PI, Math.random() * Math.PI, Math.random() * Math.PI);
      webglScene.add(object);
    }
  }

  function createContentSlides() {
    const sourceElements = document.querySelectorAll('#content-source .content-section');
    sourceElements.forEach((el, i) => {
      const positionOnCurve = raceTrackCurve.getPointAt(slideProgress[i]);
      const cssObject = new CSS3DObject(el);
      cssObject.position.copy(positionOnCurve);
      cssScene.add(cssObject);
    });
    calculateWaypoints();
  }

  function calculateWaypoints() {
    const scrollContainerHeight = document.querySelector('.scroll-container').scrollHeight;
    const windowHeight = window.innerHeight;
    const totalScrollable = scrollContainerHeight - windowHeight;

    waypoints.length = 0;
    slideProgress.forEach(progress => {
      waypoints.push(progress * totalScrollable);
    });
  }

  function animate() {
    requestAnimationFrame(animate);
    webglRenderer.render(webglScene, camera);
    cssRenderer.render(cssScene, camera);
  }

  function handleScroll() {
    const scrollPosition = window.scrollY;
    const scrollContainerHeight = document.querySelector('.scroll-container').scrollHeight;
    const windowHeight = window.innerHeight;
    const totalScrollable = scrollContainerHeight - windowHeight;

    if (totalScrollable <= 0) return;

    let scrollFraction = scrollPosition / totalScrollable;
    scrollFraction = Math.min(Math.max(scrollFraction, 0), 1);

    const contentPoint = raceTrackCurve.getPointAt(scrollFraction);
    // ** CORRECCIÓN: Asegurarse de que lookAtPoint no sea igual a contentPoint al final **
    const lookAtPoint = raceTrackCurve.getPointAt(Math.min(scrollFraction + 0.001, 1));

    const direction = new THREE.Vector3().subVectors(contentPoint, lookAtPoint).normalize();
    const cameraPosition = contentPoint.clone().addScaledVector(direction, 800);

    camera.position.copy(cameraPosition);
    pointLight.position.copy(cameraPosition);
    camera.lookAt(contentPoint);

    cssScene.children.forEach(object => {
      object.rotation.copy(camera.rotation);
    });

    // Lógica para el overlay
    const introCard = document.querySelector('#intro-card .text-box');
    if (introCard) {
      const fadeEnd = 300;
      const opacity = 1 - Math.min(scrollPosition / fadeEnd, 1);
      introCard.style.backgroundColor = `rgba(0, 0, 0, ${opacity})`;
    }

    // Manejo de precarga de videos
    handleVideoPreloading(scrollPosition, totalScrollable);
  }

  // Sistema de precarga de videos
  const loadedVideos = new Set();
  const preloadDistance = 0.1; // Precargar cuando esté a 10% de distancia
  const thumbnailCache = new Map();

  // Función para obtener thumbnail de Vimeo
  async function getVimeoThumbnail(videoId) {
    if (thumbnailCache.has(videoId)) {
      return thumbnailCache.get(videoId);
    }

    try {
      const response = await fetch(`https://vimeo.com/api/v2/video/${videoId}.json`);
      const data = await response.json();
      const thumbnailUrl = data[0].thumbnail_large;
      thumbnailCache.set(videoId, thumbnailUrl);
      return thumbnailUrl;
    } catch (error) {
      console.warn(`No se pudo obtener thumbnail para video ${videoId}:`, error);
      // Fallback a una imagen genérica o mantener la actual
      return `https://i.vimeocdn.com/video/${videoId}-640x360.jpg`;
    }
  }

  // Función para cargar thumbnails reales
  async function loadRealThumbnails() {
    const videoContainers = document.querySelectorAll('.video-placeholder[data-vimeo-id]');

    for (const container of videoContainers) {
      const videoId = container.getAttribute('data-vimeo-id');
      const thumbnail = container.querySelector('.video-thumbnail');

      if (videoId && thumbnail) {
        try {
          const thumbnailUrl = await getVimeoThumbnail(videoId);
          thumbnail.style.backgroundImage = `url('${thumbnailUrl}')`;
        } catch (error) {
          console.warn(`Error cargando thumbnail para video ${videoId}:`, error);
        }
      }
    }
  }

  let lastScrollPosition = 0;
  let scrollDirection = 1; // 1 para abajo, -1 para arriba

  function handleVideoPreloading(scrollPosition, totalScrollable) {
    const currentProgress = scrollPosition / totalScrollable;

    // Detectar dirección del scroll
    scrollDirection = scrollPosition > lastScrollPosition ? 1 : -1;
    lastScrollPosition = scrollPosition;

    // Obtener todos los contenedores de video
    const videoContainers = document.querySelectorAll('.video-placeholder');

    videoContainers.forEach((container, index) => {
      const slideProgress = index / (videoContainers.length - 1);
      const distance = Math.abs(currentProgress - slideProgress);

      // Precargar videos que están cerca, priorizando la dirección del scroll
      const shouldPreload = distance <= preloadDistance ||
                           (scrollDirection > 0 && slideProgress > currentProgress && slideProgress - currentProgress <= preloadDistance * 2) ||
                           (scrollDirection < 0 && slideProgress < currentProgress && currentProgress - slideProgress <= preloadDistance * 2);

      if (shouldPreload && !loadedVideos.has(container)) {
        preloadVideo(container);
      }
    });
  }

  function preloadVideo(container) {
    const iframe = container.querySelector('iframe');
    const thumbnail = container.querySelector('.video-thumbnail');
    const spinner = container.querySelector('.loading-spinner');
    const playButton = container.querySelector('.play-button');

    if (!iframe || loadedVideos.has(container)) return;

    // Marcar como cargado para evitar múltiples cargas
    loadedVideos.add(container);

    // Mostrar spinner y ocultar botón de play
    if (spinner) spinner.style.display = 'block';
    if (playButton) playButton.style.display = 'none';

    // Cargar el video
    const dataSrc = iframe.getAttribute('data-src');
    if (dataSrc) {
      iframe.src = dataSrc;

      // Manejar cuando el iframe esté listo
      iframe.onload = () => {
        // Esperar un poco más para asegurar que el video esté completamente cargado
        setTimeout(() => {
          if (spinner) spinner.style.display = 'none';
          iframe.classList.add('loaded');
          if (thumbnail) {
            thumbnail.classList.add('hidden');
          }
        }, 1500); // Aumentado el delay para mejor experiencia
      };

      // Manejar errores de carga
      iframe.onerror = () => {
        console.warn('Error cargando video:', dataSrc);
        if (spinner) spinner.style.display = 'none';
        if (playButton) playButton.style.display = 'flex';
        loadedVideos.delete(container); // Permitir reintentar
      };
    }
  }

  // Manejar clicks en thumbnails para cargar video inmediatamente
  function setupThumbnailClicks() {
    const thumbnails = document.querySelectorAll('.video-thumbnail');
    thumbnails.forEach(thumbnail => {
      thumbnail.addEventListener('click', (e) => {
        e.preventDefault();
        const container = thumbnail.closest('.video-placeholder');
        if (container && !loadedVideos.has(container)) {
          preloadVideo(container);
        }
      });
    });
  }

  function handleKeyNavigation(e) {
    if (e.key !== 'ArrowRight' && e.key !== 'ArrowLeft') return;

    const currentScroll = window.scrollY;
    let targetIndex;

    if (e.key === 'ArrowRight') {
      targetIndex = waypoints.findIndex(w => w > currentScroll + 1);
      if (targetIndex === -1) targetIndex = waypoints.length - 1;
    } else { // ArrowLeft
      const prevWaypoints = waypoints.filter(w => w < currentScroll - 1);
      targetIndex = prevWaypoints.length > 0 ? waypoints.indexOf(prevWaypoints[prevWaypoints.length - 1]) : 0;
    }

    window.scrollTo({
      top: waypoints[targetIndex],
      behavior: 'smooth'
    });
  }

  function onWindowResize() {
    camera.aspect = window.innerWidth / window.innerHeight;
    camera.updateProjectionMatrix();
    webglRenderer.setSize(window.innerWidth, window.innerHeight);
    cssRenderer.setSize(window.innerWidth, window.innerHeight);
    calculateWaypoints();
  }

  const jukeboxGrid = document.querySelector('#jukebox-section .jukebox-grid');
  const videoModal = document.getElementById('video-modal');
  const vimeoPlayer = document.getElementById('vimeo-player');
  const closeModalBtn = document.querySelector('.close-modal');

  function populateJukebox() {
    if (!jukeboxGrid) return;
    jukeboxGrid.innerHTML = '';
    videos.forEach(video => {
      const card = document.createElement('div');
      card.classList.add('video-card');
      card.innerHTML = `<img src="${video.thumbnail}" alt="Miniatura de ${video.title}"><div class="video-card-title">${video.title}</div>`;
      card.addEventListener('click', () => openModal(video.vimeoId));
      jukeboxGrid.appendChild(card);
    });
  }

  function openModal(vimeoId) {
    vimeoPlayer.src = `https://player.vimeo.com/video/${vimeoId}?autoplay=1&color=dbfe0d&title=0&byline=0&portrait=0`;
    videoModal.style.display = 'flex';
  }

  function closeModal() {
    vimeoPlayer.src = '';
    videoModal.style.display = 'none';
  }

  closeModalBtn.addEventListener('click', closeModal);
  videoModal.addEventListener('click', (e) => { if (e.target === videoModal) closeModal(); });

  init();
  populateJukebox();
</script>
</body>
</html>
